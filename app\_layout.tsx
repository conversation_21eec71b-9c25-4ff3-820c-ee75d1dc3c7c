import { useEffect, useState } from "react";
import { useFonts } from "expo-font";
import { Stack, useRouter } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import { StatusBar } from "expo-status-bar";
import "react-native-reanimated";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import "../i18n";
import { ThemeProvider } from "../context/ThemeContext";
import { I18nManager } from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useTranslation } from "react-i18next";
import { GoogleSignin } from "@react-native-google-signin/google-signin";

SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const router = useRouter();
  const [loaded] = useFonts({
    SpaceMono: require("../assets/fonts/SpaceMono-Regular.ttf"),
  });
  const [languageInitialized, setLanguageInitialized] = useState(false);
  const [hasNavigated, setHasNavigated] = useState(false);
  const { i18n } = useTranslation();

  useEffect(() => {
    GoogleSignin.configure({
      webClientId: "595684359316-muh5ti47so3h87t9agb9nmfvoqpq4oj1.apps.googleusercontent.com",
      profileImageSize: 150,
    });
    const initializeLanguage = async () => {
      try {
        const savedLanguage = await AsyncStorage.getItem("user-language");
        if (savedLanguage) {
          await i18n.changeLanguage(savedLanguage);
          if (savedLanguage === "ar") {
            I18nManager.forceRTL(true);
          } else {
            I18nManager.forceRTL(false);
          }
        }
        setLanguageInitialized(true);
      } catch (error) {
        console.error("Error initializing language:", error);
        setLanguageInitialized(true); // Still proceed even if language init fails
      }
    };

    initializeLanguage();
  }, []);

  useEffect(() => {
    const hideSplashScreen = async () => {
      if (loaded && languageInitialized && !hasNavigated) {
        try {
          await SplashScreen.hideAsync();
          // Navigate to login screen after splash screen is hidden
          router.replace("/auth/login");
          setHasNavigated(true);
        } catch (error) {
          console.error("Error hiding splash screen:", error);
          // Still navigate even if splash screen hiding fails
          router.replace("/auth/login");
          setHasNavigated(true);
        }
      }
    };

    hideSplashScreen();
  }, [loaded, languageInitialized, hasNavigated]);

  // Always render the navigator, even during initialization
  // The splash screen will handle the loading state

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <ThemeProvider>
        <Stack screenOptions={{ headerShown: false }}>
          <Stack.Screen name="club/[id]" options={{ headerShown: false }} />
          <Stack.Screen name="member/[id]/subscription" options={{ headerShown: false }} />
          <Stack.Screen name="member/[id]" options={{ headerShown: false }} />
          <Stack.Screen name="auth/login" options={{ headerShown: false }} />
          <Stack.Screen name="auth/register" options={{ headerShown: false }} />
          <Stack.Screen name="+not-found" />
        </Stack>
        <StatusBar style="auto" />
      </ThemeProvider>
    </GestureHandlerRootView>
  );
}
