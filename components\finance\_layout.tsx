import { Stack } from "expo-router";
import { Colors } from "@/constants/Colors";
import { useTheme } from "@/context/ThemeContext";
import { StatusBar } from "expo-status-bar";
import { TouchableOpacity, View, Text, TextInput, Modal, StyleSheet, ScrollView } from "react-native";
import { MapPin, Plus, Search } from "lucide-react-native";
import { useRouter } from "expo-router";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { useState } from "react";

interface SettingsModalProps {
  visible: boolean;
  onClose: () => void;
}

const AddExpenseModal = ({ visible, onClose }: SettingsModalProps) => {
  const handleSaveChanges = () => {
    // TODO: Implement save logic
    onClose();
  };

  const theme = {
    background: "#fff",
    text: "#000",
    secondaryText: "#666",
    border: "#eee",
    card: "#fff",
    sectionHeader: "#000",
    categoryTag: "#007AFF",
    input: "#f5f5f5",
  };

  return (
    <Modal visible={visible} transparent animationType="fade" onRequestClose={onClose}>
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <Text style={styles.title}>Add Expense</Text>
          </View>

          <ScrollView style={styles.addExpenseForm}>
            <View style={styles.formGroup}>
              <Text style={[styles.label, { color: theme.text }]}>Name</Text>
              <TextInput
                style={[styles.input, { backgroundColor: theme.input, color: theme.text }]}
                placeholder="Enter expense name"
                placeholderTextColor={theme.secondaryText}
              />
            </View>
            <View style={styles.formGroup}>
              <Text style={[styles.label, { color: theme.text }]}>Expense Description</Text>
              <TextInput
                style={[styles.input, { backgroundColor: theme.input, color: theme.text }]}
                placeholder="Example: Monthly electricity bill"
                placeholderTextColor={theme.secondaryText}
              />
            </View>
            <View style={styles.formGroup}>
              <Text style={[styles.label, { color: theme.text }]}>Payment Date</Text>
              <TextInput
                style={[styles.input, { backgroundColor: theme.input, color: theme.text }]}
                placeholder="Enter payment date"
                placeholderTextColor={theme.secondaryText}
              />
            </View>
            <View style={styles.formGroup}>
              <Text style={[styles.label, { color: theme.text }]}>Amount</Text>
              <TextInput
                style={[styles.input, { backgroundColor: theme.input, color: theme.text }]}
                placeholder="Enter amount"
                placeholderTextColor={theme.secondaryText}
              />
            </View>
          </ScrollView>

          <View style={styles.footer}>
            <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.saveButton} onPress={handleSaveChanges}>
              <Text style={styles.saveButtonText}>Save Changes</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const Layout = () => {
  const { isDark } = useTheme();
  const router = useRouter();
  const [isAddExpenseModalVisible, setIsAddExpenseModalVisible] = useState(false);

  const backgroundColor = Colors.light.background;

  return (
    <>
      <Stack
        screenOptions={{
          headerShadowVisible: false,
          contentStyle: { backgroundColor: backgroundColor },
        }}
      >
        <Stack.Screen
          name="index"
          options={{
            title: "Finance",
            headerLargeTitle: true,
            headerTitle: "Finance",
            // headerSearchBarOptions: {
            //   placeholder: 'Find your perfect gym',
            //   tintColor: Colors.light.text,
            // },

            headerRight: () => (
              <TouchableOpacity style={[styles.addButton]} onPress={() => setIsAddExpenseModalVisible(!isAddExpenseModalVisible)}>
                <Plus size={24} color={Colors["light"].tint} />
                <Text
                  style={{
                    color: "#E13D3B",
                    marginLeft: 8,
                    fontWeight: "600",
                  }}
                >
                  Add Expense
                </Text>
              </TouchableOpacity>
            ),
          }}
        />
      </Stack>
      <StatusBar style="auto" />
      <AddExpenseModal visible={isAddExpenseModalVisible} onClose={() => setIsAddExpenseModalVisible(false)} />
    </>
  );
};

const styles = StyleSheet.create({
  headerRight: {
    flexDirection: "row",
    alignItems: "center",
    paddingRight: 16,
    gap: 8,
  },
  cityText: {
    fontSize: 16,
    fontWeight: "500",
  },
  addButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 8,
    borderRadius: 8,
    paddingRight: 16,
  },
  addButtonText: {
    color: "#E13D3B",
    marginLeft: 8,
    fontWeight: "600",
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: 16,
    marginBottom: 8,
    paddingHorizontal: 12,
    height: 44,
    borderRadius: 8,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    height: "100%",
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContent: {
    width: "90%",
    maxHeight: "80%",
    backgroundColor: "white",
    borderRadius: 20,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  header: {
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#1a1a1a",
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: "#666",
  },
  addExpenseForm: {
    marginBottom: 20,
  },
  settingItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  settingInfo: {
    flex: 1,
    marginRight: 16,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1a1a1a",
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: "#666",
  },
  footer: {
    flexDirection: "row",
    justifyContent: "flex-end",
    gap: 12,
  },
  saveButton: {
    backgroundColor: Colors["light"].tint,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  saveButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
  cancelButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  cancelButtonText: {
    color: "#666",
    fontSize: 16,
    fontWeight: "600",
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "600",
    color: "#000",
  },
  headerSubtitle: {
    fontSize: 12,
    color: "#666",
    marginTop: 2,
    maxWidth: 300,
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 15,
    fontWeight: "600",
    marginBottom: 10,
    opacity: 0.8,
  },
  input: {
    height: 44,
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.1)",
  },
});

export default Layout;
