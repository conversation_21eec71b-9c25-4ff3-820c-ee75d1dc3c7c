import { Colors } from "@/constants/Colors";
import { Stack } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { useTranslation } from "react-i18next";

const Layout = () => {
  const backgroundColor = Colors.light.background;
  const { t } = useTranslation();

  return (
    <>
      <Stack
        screenOptions={{
          headerShadowVisible: false,
          contentStyle: { backgroundColor: backgroundColor },
        }}
      >
        <Stack.Screen
          name="index"
          options={{
            title: t("members"),
            headerLargeTitle: true,
            headerTitle: t("members"),
            // headerSearchBarOptions: {
            //   placeholder: 'Find your perfect gym',
            //   tintColor: Colors.light.text,
            // },
          }}
        />
      </Stack>
      <StatusBar style="auto" />
    </>
  );
};

export default Layout;
