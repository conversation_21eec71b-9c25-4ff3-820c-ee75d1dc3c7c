import ApiService from "./api.service";

class ClubService {
  // Get all clubs
  static async getAllClubs() {
    try {
      const res = await ApiService.get("/api/Clubs");
      return res;
    } catch (error) {
      console.error("Error fetching clubs:", error);
      throw error;
    }
  }

  // Get club by ID
  static async getClubById(id) {
    try {
      return await ApiService.get(`/api/Clubs/${id}`);
    } catch (error) {
      console.error("Error fetching club details:", error);
      throw error;
    }
  }

  // Create new club
  static async createClub(clubData) {
    try {
      return await ApiService.post("/api/Clubs/create", clubData);
    } catch (error) {
      console.error("Error creating club:", error);
      throw error;
    }
  }

  // Get user's club
  static async getMyClub() {
    try {
      return await ApiService.get("/api/Clubs/myclub");
    } catch (error) {
      console.error("Error fetching my club:", error);
      throw error;
    }
  }

  // Update club
  static async updateClub(clubData) {
    try {
      return await ApiService.put("/api/Clubs/update", clubData);
    } catch (error) {
      console.error("Error updating club:", error);
      throw error;
    }
  }

  // get my club settings
  static async getMyClubSettings() {
    try {
      return await ApiService.get("/api/Settings/myclub");
    } catch (error) {
      console.error("Error fetching my club settings:", error);
      throw error;
    }
  }

  // Update club settings
  static async updateClubSettings(settingsData) {
    try {
      const response = await ApiService.put("/api/Settings/myclub/update", settingsData);
      console.log("Update successful:", response);
      return response;
    } catch (error) {
      console.error("Error updating club settings:", {
        error: error,
        requestData: settingsData,
        response: error.response?.data, // if available
      });
      throw error;
    }
  }

  // Get club settings by club ID
  static async getClubSettings(clubId) {
    try {
      return await ApiService.get(`/api/settings/club/${clubId}`);
    } catch (error) {
      console.error("Error fetching club settings:", error);
      throw error;
    }
  }

  // Get club rating by club ID
  static async getClubRating(clubId) {
    try {
      return await ApiService.get(`/api/ratings/club/${clubId}`);
    } catch (error) {
      console.error("Error fetching club rating:", error);
      throw error;
    }
  }
}

export default ClubService;
