import ApiService from "./api.service";
import { jwtDecode } from "jwt-decode";
import TokenService from "./token.service";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Platform } from "react-native";
import * as AuthSession from "expo-auth-session";
import { getGoogleClientId, GOOGLE_CONFIG, GOOGLE_SCOPES } from "../config/google.auth.config";
import TokenValidatorService from "./token-validator.service";
import Constants from "expo-constants";

interface LoginCredentials {
  username: string;
  password: string;
}

interface GoogleAuthResponse {
  type: "success" | "error" | "cancel" | "redirect";
  params?: {
    access_token?: string;
    id_token?: string;
    code?: string;
    error?: string;
  };
  authentication?: AuthSession.TokenResponse;
}

interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
}

interface MemberRegisterData extends RegisterData {
  clubId: string;
}

interface FacebookAuthData {
  accessToken: string;
}

interface PasswordChange {
  currentPassword: string;
  newPassword: string;
}

interface PasswordReset {
  email: string;
  token: string;
  newPassword: string;
}

class AuthService {
  // Token management methods now delegate to TokenService
  static async setTokens(accessToken: string, refreshToken: string) {
    return TokenService.setTokens(accessToken, refreshToken);
  }

  static async getToken(): Promise<string | null> {
    return await TokenService.getToken();
  }

  static async getRefreshToken(): Promise<string | null> {
    return await TokenService.getRefreshToken();
  }

  static async clearTokens() {
    await TokenService.clearTokens();
  }

  static async validateSession(): Promise<boolean> {
    const getToken = async () => await this.getToken();
    return await TokenValidatorService.validateSession(getToken);
  }

  static setupPeriodicValidation(intervalMinutes = 5) {
    const onInvalidToken = async () => {
      await this.forceLogout();
      // You can add any additional cleanup or navigation here
    };

    return TokenValidatorService.setupPeriodicValidation(intervalMinutes, onInvalidToken);
  }

  static async forceLogout() {
    await TokenService.clearTokens();
  }

  // Authentication endpoints
  static async login(credentials: LoginCredentials) {
    try {
      const response = await ApiService.post("/api/Account/login", credentials);
      if (response.accessToken && response.refreshToken) {
        await this.setTokens(response.accessToken, response.refreshToken);
      }
      return response;
    } catch (error) {
      console.error("Login failed:", error);
      throw error;
    }
  }

  static async register(data: RegisterData) {
    try {
      return await ApiService.post("/api/Account/register", data);
    } catch (error) {
      console.error("Registration failed:", error);
      throw error;
    }
  }

  static async memberRegister(data: MemberRegisterData) {
    try {
      return await ApiService.post("/api/Account/member/register", data);
    } catch (error) {
      console.error("Member registration failed:", error);
      throw error;
    }
  }

  // Facebook authentication
  static async registerWithFacebook(data: FacebookAuthData) {
    try {
      return await ApiService.post("/api/Account/register/facebook", data);
    } catch (error) {
      console.error("Facebook registration failed:", error);
      throw error;
    }
  }

  static async authenticateWithFacebook(data: FacebookAuthData) {
    try {
      const response = await ApiService.post("/api/Account/authenticate/facebook", data);
      if (response.token && response.refreshToken) {
        await this.setTokens(response.token, response.refreshToken);
        // Map token to accessToken for consistency with the login page
        response.accessToken = response.token;
      }
      return response;
    } catch (error) {
      console.error("Facebook authentication failed:", error);
      throw error;
    }
  }

  static async updateToken() {
    try {
      return await ApiService.get("/api/Account/update-token");
    } catch (error) {
      console.error("Token update failed:", error);
      throw error;
    }
  }

  // User profile endpoints
  static async getMemberProfile() {
    try {
      return await ApiService.get("/api/Account/members/me");
    } catch (error) {
      console.error("Failed to fetch member profile:", error);
      throw error;
    }
  }

  static async getClubOwnerProfile() {
    try {
      return await ApiService.get("/api/Account/clubowner/me");
    } catch (error) {
      console.error("Failed to fetch club owner profile:", error);
      throw error;
    }
  }

  static async getAdminProfile() {
    try {
      return await ApiService.get("/api/Account/admin/me");
    } catch (error) {
      console.error("Failed to fetch admin profile:", error);
      throw error;
    }
  }

  static async updateProfile(data: Partial<RegisterData>) {
    try {
      return await ApiService.put("/api/Account/me", data);
    } catch (error) {
      console.error("Profile update failed:", error);
      throw error;
    }
  }

  // Password and email management
  static async changePassword(data: PasswordChange) {
    try {
      return await ApiService.put("/api/Account/change-password", data);
    } catch (error) {
      console.error("Password change failed:", error);
      throw error;
    }
  }

  static async validateEmail(email: string) {
    try {
      return await ApiService.put(`/api/Account/validate-email/${email}`);
    } catch (error) {
      console.error("Email validation failed:", error);
      throw error;
    }
  }

  static async checkEmailValidation() {
    try {
      return await ApiService.get("/api/Account/me/is-email-validated");
    } catch (error) {
      console.error("Email validation check failed:", error);
      throw error;
    }
  }

  static async resetPassword(data: PasswordReset) {
    try {
      return await ApiService.put("/api/Account/reset-password", data);
    } catch (error) {
      console.error("Password reset failed:", error);
      throw error;
    }
  }

  // Role testing
  static async testRole() {
    try {
      return await ApiService.get("/api/Account/test-role");
    } catch (error) {
      console.error("Role test failed:", error);
      throw error;
    }
  }

  static decodeUserRole(token: string): string {
    try {
      const decodedToken: any = jwtDecode(token);
      return decodedToken["http://schemas.microsoft.com/ws/2008/06/identity/claims/role"] || "user";
    } catch (error) {
      console.error("Error decoding token:", error);
      return "user";
    }
  }

  static decodeUser(token: string) {
    const decodedToken = jwtDecode(token);
    return decodedToken;
  }

  // Google authentication
  static async authenticateWithGoogle(data: { code: string; state?: string; scope?: string; authuser?: string; prompt?: string }) {
    try {
      // Convert data object to query string
      const queryParams = new URLSearchParams();
      Object.entries(data).forEach(([key, value]) => {
        if (value) queryParams.append(key, value);
      });

      const response = await ApiService.get(`/signin-google?${queryParams.toString()}`);

      if (response.accessToken && response.refreshToken) {
        await this.setTokens(response.accessToken, response.refreshToken);
      }
      return response;
    } catch (error) {
      console.error("Google authentication failed:", error);
      throw error;
    }
  }

  private static createGoogleAuthRequest() {
    const isDevelopment = process.env.NODE_ENV === "development" || __DEV__;
    const scheme = isDevelopment ? "exp" : "com.murad.elm.gogymium";

    const redirectUri = AuthSession.makeRedirectUri({
      scheme,
      path: "auth/login/callback",
    });

    console.log("Google Auth Redirect URI:", redirectUri);

    return {
      clientId: getGoogleClientId(),
      scopes: ["openid", "profile", "email"],
      redirectUri,
      responseType: AuthSession.ResponseType.Code,
      additionalParameters: {
        prompt: "select_account",
        access_type: "offline",
      },
    };
  }

  /**
   * Perform Google Sign-In using expo-auth-session
   */
  static async signInWithGoogle(): Promise<GoogleAuthResponse> {
    try {
      const authRequest = new AuthSession.AuthRequest({
        ...this.createGoogleAuthRequest(),
        usePKCE: true,
      });

      const discovery = await AuthSession.fetchDiscoveryAsync("https://accounts.google.com");

      // For web, we need to handle the redirect manually
      if (Platform.OS === "web") {
        // Check if we're coming back from Google OAuth
        const url = new URL(window.location.href);
        const code = url.searchParams.get("code");

        if (code) {
          // We're in the callback - handle the code
          try {
            const response = await this.authenticateWithGoogle({ code });
            // Clean up the URL
            window.history.replaceState({}, document.title, "/auth/login");
            return {
              type: "success",
              params: { code },
              authentication: { accessToken: response.accessToken },
            } as GoogleAuthResponse;
          } catch (error) {
            console.error("Google authentication failed:", error);
            return {
              type: "error",
              params: {
                error: error instanceof Error ? error.message : "Authentication failed",
              },
            };
          }
        }

        // If no code, start the OAuth flow
        const authUrl = await authRequest.makeAuthUrlAsync(discovery);
        window.location.href = authUrl;
        return { type: "redirect" } as GoogleAuthResponse;
      }

      // For mobile, use promptAsync
      const result = await authRequest.promptAsync(discovery);
      return result as GoogleAuthResponse;
    } catch (error) {
      console.error("Google Sign-In error:", error);
      return {
        type: "error",
        params: {
          error: error instanceof Error ? error.message : "Unknown error",
        },
      };
    }
  }

  /**
   * Complete Google authentication flow
   */
  static async performGoogleAuth() {
    try {
      // On web, we'll handle the callback in the callback page
      if (Platform.OS === "web") {
        // This will be handled by the callback page
        const googleResponse = await this.signInWithGoogle();
        return { status: "processing" };
      }

      // For mobile, handle the OAuth flow
      const googleResponse = await this.signInWithGoogle();

      if (googleResponse.type !== "success" || !googleResponse.params?.code) {
        throw new Error(googleResponse.params?.error || "Google Sign-In failed: No authorization code received");
      }

      // For mobile, authenticate with the backend using the code
      const authData = {
        provider: "google",
        code: googleResponse.params.code,
      };

      const response = await ApiService.post("/api/Account/authenticate/google", authData);

      if (response?.accessToken && response?.refreshToken) {
        await this.setTokens(response.accessToken, response.refreshToken);
        return response;
      }

      throw new Error("Authentication failed: No tokens received");
    } catch (error) {
      console.error("Google authentication error:", error);
      throw error;
    }
  }
}

export default AuthService;
