import { jwtDecode } from "jwt-decode";
import AsyncStorage from "@react-native-async-storage/async-storage";

class TokenValidatorService {
  // Check if token is valid (not expired and properly formatted)
  static isTokenValid(token: string | null): boolean {
    if (!token) return false;

    try {
      const decodedToken = jwtDecode(token);
      const currentTime = Date.now() / 1000;
      return decodedToken.exp && decodedToken.exp > currentTime;
    } catch (error) {
      console.error("Error validating token:", error);
      return false;
    }
  }

  // Check token on app start and during navigation
  static async validateSession(getToken: () => Promise<string | null>): Promise<boolean> {
    try {
      const token = await getToken();
      return !!(token && this.isTokenValid(token));
    } catch (error) {
      console.error("Session validation error:", error);
      return false;
    }
  }

  // Force logout without direct router usage
  static async forceLogout(showSessionExpiredMessage = true) {
    try {
      console.log("Logging out due to invalid or expired session");
      
      // Clear tokens
      await AsyncStorage.multiRemove(["token", "refreshToken", "userSession"]);
      
      // Additional cleanup if needed
      if (showSessionExpiredMessage) {
        // You can show a toast or alert here if needed
        console.log("Your session has expired. Please log in again.");
      }
    } catch (error) {
      console.error("Error during logout:", error);
    }
  }

  // Set up periodic token validation
  static setupPeriodicValidation(intervalMinutes = 5, onInvalidToken: () => Promise<void>) {
    // Check token validity every X minutes
    const intervalId = setInterval(async () => {
      const token = await AsyncStorage.getItem("token");
      if (!this.isTokenValid(token)) {
        await onInvalidToken();
      }
    }, intervalMinutes * 60 * 1000);

    return intervalId; // Return interval ID so it can be cleared if needed
  }
}

export default TokenValidatorService;
