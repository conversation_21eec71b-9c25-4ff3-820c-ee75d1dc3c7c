import React, { useEffect, useState } from "react";
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image, I18nManager } from "react-native";
import { useRouter } from "expo-router";
import { useTheme } from "@/context/ThemeContext";
import { Ionicons } from "@expo/vector-icons";
import { Bell } from "lucide-react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Colors } from "@/constants/Colors";
import AsyncStorage from "@react-native-async-storage/async-storage";
import AuthService from "@/services/auth.service";
import SubscriptionService from "@/services/subscription.service";
import { useTranslation } from "react-i18next";

type Subscription = {
  id: number;
  type: "Monthly" | "Yearly";
  status: "Active" | "Expired";
  startDate: string;
  endDate: string;
  price: number;
  gymName: string;
};

const formatDate = (dateString: string) => {
  if (!dateString) return "-";
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

export default function SubscriptionScreen() {
  const { isDark } = useTheme();
  const router = useRouter();
  const { top } = useSafeAreaInsets();
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const { t, i18n } = useTranslation();
  const [user, setUser] = useState<any | null>(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  const isRTL = i18n.language === "ar";

  useEffect(() => {
    const checkUserSession = async () => {
      try {
        const userSession = await AsyncStorage.getItem("userSession");
        setIsLoggedIn(!!userSession);
        if (userSession) {
          const refetchSubscriptions = await AuthService.getMemberProfile();
          setUser(refetchSubscriptions);
        }
      } catch (error) {
        console.error("Error checking user session:", error);
      }
    };

    checkUserSession();
  }, []);

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text
        style={[
          styles.emptyTitle,
          {
            color: Colors["light"].text,
            textAlign: isRTL ? "right" : "center",
          },
        ]}
      >
        {t("noActiveSubscriptions")}
      </Text>
      <Text
        style={[
          styles.emptySubtitle,
          {
            color: Colors["light"].secondary,
            textAlign: isRTL ? "right" : "center",
          },
        ]}
      >
        {t("noSubscriptionsDescription")}
      </Text>
      <TouchableOpacity style={[styles.exploreButton, { backgroundColor: Colors["light"].text }]} onPress={() => router.push("/(tabs)/user/clubs")}>
        <Text style={styles.exploreButtonText}>{t("exploreClubs")}</Text>
      </TouchableOpacity>
    </View>
  );

  const onLoginRenderState = () => (
    <View style={styles.emptyContainer}>
      <Text
        style={[
          styles.emptyTitle,
          {
            color: Colors["light"].text,
            textAlign: isRTL ? "right" : "center",
          },
        ]}
      >
        {t("pleaseLoginSubscriptions")}
      </Text>
      {/* <Text style={[styles.emptySubtitle, { color: Colors['light'].secondary }]}>
        You haven't subscribed to any gym or fitness program yet.
      </Text> */}
      <TouchableOpacity style={[styles.exploreButton, { backgroundColor: Colors["light"].text }]} onPress={() => router.push("/auth/login")}>
        <Text style={styles.exploreButtonText}>{t("goToLogin")}</Text>
      </TouchableOpacity>
    </View>
  );

  const renderSubscriptionCard = (subscription: any) => (
    <View
      key={subscription.id}
      style={[
        styles.subscriptionCard,
        {
          backgroundColor: "#FFFFFF",
          borderColor: "rgba(0,0,0,0.1)",
        },
      ]}
    >
      <View style={[styles.cardHeader, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
        <View style={{ flex: 1, cursor: "pointer" }}>
          <Text
            style={[
              styles.gymName,
              {
                color: Colors["light"].text,
                textAlign: isRTL ? "right" : "left",
              },
            ]}
          >
            {subscription.group.club.name}
          </Text>
          <Text
            style={[
              styles.groupName,
              {
                color: Colors["light"].secondary,
                textAlign: isRTL ? "right" : "left",
              },
            ]}
          >
            {subscription.group.name}
          </Text>
        </View>
        <Text
          style={[
            styles.statusBadge,
            {
              backgroundColor: subscription.subscriptionStatus.label !== "Suspended" ? "rgba(46, 204, 113, 0.1)" : "rgba(231, 76, 60, 0.1)",
              color: subscription.subscriptionStatus.label !== "Suspended" ? "#2ECC71" : "#E74C3C",
            },
          ]}
        >
          {t(subscription.subscriptionStatus.label.toLowerCase())}
        </Text>
      </View>

      <View style={styles.cardDetails}>
        {/* Subscription Period */}
        <View>
          <View style={[styles.detailRow, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
            <Text
              style={[
                styles.detailLabel,
                {
                  color: Colors["light"].secondary,
                  textAlign: isRTL ? "right" : "left",
                },
              ]}
            >
              {t("startDate")}
            </Text>
            <Text
              style={[
                styles.detailValue,
                {
                  color: Colors["light"].text,
                  textAlign: isRTL ? "left" : "right",
                },
              ]}
            >
              {subscription.startDate ? formatDate(subscription.startDate) : "-"}
            </Text>
          </View>
          <View style={[styles.detailRow, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
            <Text
              style={[
                styles.detailLabel,
                {
                  color: Colors["light"].secondary,
                  textAlign: isRTL ? "right" : "left",
                },
              ]}
            >
              {t("endDate")}
            </Text>
            <Text
              style={[
                styles.detailValue,
                {
                  color: Colors["light"].text,
                  textAlign: isRTL ? "left" : "right",
                },
              ]}
            >
              {subscription.endDate ? formatDate(subscription.endDate) : "-"}
            </Text>
          </View>
          <View style={[styles.detailRow, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
            <Text
              style={[
                styles.detailLabel,
                {
                  color: Colors["light"].secondary,
                  textAlign: isRTL ? "right" : "left",
                },
              ]}
            >
              {t("price")}
            </Text>
            <Text
              style={[
                styles.detailValue,
                {
                  color: Colors["light"].text,
                  textAlign: isRTL ? "left" : "right",
                },
              ]}
            >
              {subscription.subscriptionPrice ? `${Number(subscription.subscriptionPrice).toFixed(2)} ${subscription.currency?.code || "DH"}` : "-"}
            </Text>
          </View>
          {subscription.subscriptionDiscount > 0 && (
            <View style={[styles.detailRow, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
              <Text
                style={[
                  styles.detailLabel,
                  {
                    color: Colors["light"].secondary,
                    textAlign: isRTL ? "right" : "left",
                  },
                ]}
              >
                {t("discount")}
              </Text>
              <Text
                style={[
                  styles.detailValue,
                  {
                    color: Colors["light"].text,
                    textAlign: isRTL ? "left" : "right",
                  },
                ]}
              >
                {subscription.subscriptionDiscount ? subscription.subscriptionDiscount + "%" : "-"}
              </Text>
            </View>
          )}
        </View>
      </View>
    </View>
  );

  return (
    <ScrollView
      style={[
        styles.container,
        {
          backgroundColor: Colors["light"].background,
          paddingTop: top,
        },
      ]}
      showsVerticalScrollIndicator={false}
      contentInsetAdjustmentBehavior="automatic"
    >
      <View style={styles.subscriptionsList}>
        {isLoggedIn
          ? user?.member?.subscriptions.length === 0
            ? renderEmptyState()
            : user?.member?.subscriptions.map(renderSubscriptionCard)
          : onLoginRenderState()}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  subscriptionsList: {
    flex: 1,
    paddingHorizontal: 20,
    paddingBottom: 20,
    marginBottom: 16,
    gap: 16,
  },
  headerButtons: {
    marginTop: 24,
    marginBottom: 16,
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 16,
  },
  headerText: {
    fontSize: 24,
    fontWeight: "bold",
  },
  rightButtons: {
    flexDirection: "row",
  },
  iconButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
    marginLeft: 8,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 20,
  },
  emptyImage: {
    width: 250,
    height: 250,
    marginBottom: 20,
  },
  emptyTitle: {
    fontSize: 22,
    fontWeight: "700",
    marginBottom: 10,
    textAlign: "center",
  },
  emptySubtitle: {
    fontSize: 16,
    textAlign: "center",
    marginBottom: 20,
  },
  exploreButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 10,
    marginTop: 10,
  },
  exploreButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
    textAlign: "center",
  },
  subscriptionCard: {
    borderRadius: 12,
    padding: 15,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 3,
  },
  cardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 15,
  },
  gymName: {
    fontSize: 18,
    fontWeight: "600",
  },
  statusBadge: {
    paddingVertical: 4,
    paddingHorizontal: 10,
    borderRadius: 20,
    fontSize: 12,
    fontWeight: "600",
  },
  cardDetails: {},
  detailRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 10,
  },
  detailLabel: {
    fontSize: 14,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: "500",
  },

  groupName: {
    fontSize: 14,
    marginTop: 4,
    fontWeight: "500",
  },
});
