import AuthService from "@/services/auth.service";
import { Ionicons } from "@expo/vector-icons";
import AsyncStorage from "@react-native-async-storage/async-storage";
import * as AuthSession from "expo-auth-session";
import { LinearGradient } from "expo-linear-gradient";
import { Link, useRouter } from "expo-router";
import { ArrowRight, DumbbellIcon, Eye, EyeOff, LucideLogOut } from "lucide-react-native";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import {
  I18nManager,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Dimensions,
  Alert,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { GoogleSignin, isSuccessResponse, isErrorWithCode, statusCodes } from "@react-native-google-signin/google-signin";

const { height: screenHeight } = Dimensions.get("window");

export default function LoginScreen() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const router = useRouter();
  const { t } = useTranslation();
  const isRTL = I18nManager.isRTL;

  useEffect(() => {
    initializeScreen();
  }, []);

  const initializeScreen = async () => {
    await checkExistingSession();
    setIsInitialLoading(false);
  };

  const checkExistingSession = async () => {
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));

      const userSession = await AsyncStorage.getItem("userSession");
      if (userSession) {
        const userData = JSON.parse(userSession);
        if (userData && userData.role) {
          redirectBasedOnRole(userData.role);
        }
      }

      const rememberedUser = await AsyncStorage.getItem("rememberedUser");
      if (rememberedUser) {
        const userData = JSON.parse(rememberedUser);
        if (userData.rememberMe) {
          setEmail(userData.username);
          setRememberMe(true);
        }
      }
    } catch (error) {
      console.error("Error checking session:", error);
    }
  };

  const redirectBasedOnRole = (role: string) => {
    switch (role) {
      case "club owner":
        router.replace("/(tabs)/owner/home");
        break;
      case "user":
        router.replace("/(tabs)/user/subscription");
        break;
      default:
        router.replace("/(tabs)/user/clubs");
    }
  };

  const validateInputs = () => {
    if (!email.trim()) {
      setErrorMessage("Please enter your email address");
      return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setErrorMessage("Please enter a valid email address");
      return false;
    }

    if (!password.trim()) {
      setErrorMessage("Please enter your password");
      return false;
    }

    if (password.length < 6) {
      setErrorMessage("Password must be at least 6 characters");
      return false;
    }

    return true;
  };

  const handleLogin = async () => {
    if (!validateInputs()) return;

    setIsLoading(true);
    setErrorMessage("");

    try {
      const response = await AuthService.login({ username: email, password });

      if (response.accessToken && response.refreshToken) {
        await AuthService.setTokens(response.accessToken, response.refreshToken);

        const userRole = AuthService.decodeUserRole(response.accessToken);

        let user: any = {
          role: userRole,
          username: email,
          email: email,
          phone: "",
          firstname: "",
          lastname: "",
          address: "",
          gender: "",
        };

        if (userRole === "club owner") {
          user = await AuthService.getClubOwnerProfile();
        } else if (userRole === "user") {
          user = await AuthService.getMemberProfile();
        }

        // Store user session
        const userSession = {
          role: userRole,
          ...user,
        };

        await AsyncStorage.setItem("userSession", JSON.stringify(userSession));

        if (rememberMe) {
          // Store credentials if remember me is checked
          await AsyncStorage.setItem(
            "rememberedUser",
            JSON.stringify({
              username: email,
              rememberMe: true,
            })
          );
        } else {
          // Clear remembered user if remember me is unchecked
          await AsyncStorage.removeItem("rememberedUser");
        }

        // Redirect based on user role
        redirectBasedOnRole(userRole);
      } else {
        setErrorMessage("Login failed. Please check your credentials and try again.");
      }
    } catch (error: any) {
      console.error("Error during login:", error);
      setErrorMessage("Login failed. Please check your credentials and try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // const handleGoogleSignIn = async () => {
  //   setIsLoading(true);
  //   try {
  //     const response = await AuthService.performGoogleAuth();

  //     // If we get a redirect response, the signInWithGoogle method will handle the redirect
  //     if (response?.status === "redirect") {
  //       return;
  //     }

  //     // Handle successful authentication
  //     if (response?.accessToken) {
  //       router.replace("/(tabs)/user/clubs");
  //     }
  //   } catch (error) {
  //     console.error("Google Sign-In error:", error);
  //     setErrorMessage("Google Sign-In failed: " + (error instanceof Error ? error.message : "Unknown error"));
  //   } finally {
  //     setIsLoading(false);
  //   }
  // };

  // Handle OAuth callback when component mounts
  useEffect(() => {
    const handleOAuthCallback = async () => {
      if (Platform.OS === "web") {
        const url = new URL(window.location.href);
        const code = url.searchParams.get("code");

        if (code) {
          setIsLoading(true);
          try {
            // This will be handled by the signInWithGoogle method
            const response = await AuthService.performGoogleAuth();
            if (response?.accessToken) {
              // Navigate to home on successful authentication
              router.replace("/(tabs)/user/clubs");
            }
          } catch (error) {
            setErrorMessage("Google Sign-In failed");
          } finally {
            setIsLoading(false);
          }
        }
      }
    };

    handleOAuthCallback();
  }, []);

  const handleGoogleSignIn = async () => {
    setIsLoading(true);
    try {
      await GoogleSignin.hasPlayServices();
      const googleAuthResponse = await GoogleSignin.signIn();

      if (isSuccessResponse(googleAuthResponse)) {
        const { idToken, user }: any = googleAuthResponse;

        const authData = {
          code: idToken,
        };

        const authResponse = await AuthService.authenticateWithGoogle(authData);
        if (authResponse?.accessToken) {
          // Navigate to home on successful authentication
          Alert.alert("Success", "You have successfully logged in.");
        } else {
          setErrorMessage("Google Sign-In failed");
        }
      }
    } catch (error) {
      console.error("Google Sign-In error:", error);
      setErrorMessage("Google Sign-In failed: " + (error instanceof Error ? error.message : "Unknown error"));
    } finally {
      setIsLoading(false);
    }
  };

  if (isInitialLoading) {
    return (
      <View style={styles.loadingContainer}>
        <View style={styles.loadingGradient}>
          <ActivityIndicator size="large" color="#DC3545" />
        </View>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardView}
        keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 20}
      >
        <View style={styles.gradientBackground}>
          {/* Header with Skip Link */}
          <View style={[styles.header, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
            <TouchableOpacity
              onPress={() => router.replace("/(tabs)/user/clubs")}
              style={[styles.skipButton, { flexDirection: isRTL ? "row-reverse" : "row" }]}
              disabled={isLoading}
            >
              <Text style={styles.skipText}>{t("skipLogin") || "Skip"}</Text>
              <ArrowRight
                size={16}
                color="rgba(0, 0, 0, 0.7)"
                style={{
                  marginLeft: isRTL ? 0 : 4,
                  marginRight: isRTL ? 4 : 0,
                }}
              />
            </TouchableOpacity>
          </View>

          {/* Main Content */}
          <View style={styles.contentContainer}>
            {/* Logo Section */}
            <View style={styles.logoContainer}>
              <View style={styles.logoPlaceholder}>
                <DumbbellIcon size={60} color="#DC3545" />
              </View>
              <Text style={styles.appTitle}>GoGymium</Text>
              <Text style={[styles.subtitle, { textAlign: isRTL ? "right" : "left" }]}>{t("signInToContinue") || "Sign in to continue"}</Text>
            </View>

            {/* Form Container */}
            <View style={styles.formContainer}>
              {/* Email Input */}
              <View style={styles.inputWrapper}>
                <Ionicons name="mail-outline" size={20} color="rgba(220, 53, 69, 0.7)" style={styles.inputIcon} />
                <TextInput
                  style={[
                    styles.input,
                    {
                      textAlign: isRTL ? "right" : "left",
                      writingDirection: isRTL ? "rtl" : "ltr",
                    },
                  ]}
                  placeholder={t("email") || "Email"}
                  placeholderTextColor="rgba(0, 0, 0, 0.5)"
                  value={email}
                  onChangeText={(text) => {
                    setEmail(text);
                    setErrorMessage("");
                  }}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoComplete="email"
                  returnKeyType="next"
                  editable={!isLoading}
                />
              </View>

              {/* Password Input */}
              <View style={styles.inputWrapper}>
                <Ionicons name="lock-closed-outline" size={20} color="rgba(220, 53, 69, 0.7)" style={styles.inputIcon} />
                <TextInput
                  style={[
                    styles.input,
                    {
                      textAlign: isRTL ? "right" : "left",
                      writingDirection: isRTL ? "rtl" : "ltr",
                    },
                  ]}
                  placeholder={t("password") || "Password"}
                  placeholderTextColor="rgba(0, 0, 0, 0.5)"
                  value={password}
                  onChangeText={(text) => {
                    setPassword(text);
                    setErrorMessage("");
                  }}
                  secureTextEntry={!showPassword}
                  returnKeyType="done"
                  onSubmitEditing={handleLogin}
                  editable={!isLoading}
                />
                <TouchableOpacity style={styles.eyeIcon} onPress={() => setShowPassword(!showPassword)} disabled={isLoading}>
                  {showPassword ? <EyeOff size={20} color="rgba(0, 0, 0, 0.2)" /> : <Eye size={20} color="rgba(0, 0, 0, 0.2)" />}
                </TouchableOpacity>
              </View>

              {/* Error Message */}
              {errorMessage ? (
                <View style={styles.errorContainer}>
                  <Ionicons name="alert-circle" size={16} color="#FF3B30" />
                  <Text style={styles.errorText}>{errorMessage}</Text>
                </View>
              ) : null}

              {/* Remember Me & Forgot Password */}
              <View style={[styles.optionsRow, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
                <TouchableOpacity
                  style={[styles.checkboxContainer, { flexDirection: isRTL ? "row-reverse" : "row" }]}
                  onPress={() => setRememberMe(!rememberMe)}
                  disabled={isLoading}
                >
                  <View style={[styles.checkbox, rememberMe && styles.checkboxChecked, { marginRight: isRTL ? 0 : 8, marginLeft: isRTL ? 8 : 0 }]}>
                    {rememberMe && <Ionicons name="checkmark" size={14} color="#DC3545" />}
                  </View>
                  <Text style={styles.checkboxText}>{t("rememberMe") || "Remember me"}</Text>
                </TouchableOpacity>

                <TouchableOpacity onPress={() => router.push("https://www.gogymium.com/Account/forgot-password")} disabled={isLoading}>
                  <Text style={styles.forgotPassword}>{t("forgotPassword") || "Forgot Password?"}</Text>
                </TouchableOpacity>
              </View>

              {/* Login Button */}
              <TouchableOpacity style={[styles.loginButton, isLoading && styles.buttonDisabled]} onPress={handleLogin} disabled={isLoading}>
                {isLoading ? (
                  <ActivityIndicator size="small" color="#ffffff" />
                ) : (
                  <Text style={styles.loginButtonText}>{t("signIn") || "Sign In"}</Text>
                )}
              </TouchableOpacity>

              {/* Divider */}
              <View style={styles.dividerContainer}>
                <View style={styles.divider} />
                <Text style={styles.dividerText}>{t("or") || "or"}</Text>
                <View style={styles.divider} />
              </View>

              {/* Google Sign In Button */}
              <TouchableOpacity
                style={[styles.googleButton, { flexDirection: isRTL ? "row-reverse" : "row" }, isLoading && styles.buttonDisabled]}
                onPress={handleGoogleSignIn}
                disabled={isLoading}
              >
                <Ionicons
                  name="logo-google"
                  size={20}
                  color="#fff"
                  style={[styles.googleIcon, { marginRight: isRTL ? 0 : 10, marginLeft: isRTL ? 10 : 0 }]}
                />
                <Text style={styles.googleButtonText}>{t("signInWithGoogle") || "Continue with Google"}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#ffffff",
  },
  keyboardView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
  },
  loadingGradient: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#ffffff",
  },
  loadingText: {
    color: "#DC3545",
    fontSize: 16,
    marginTop: 16,
    fontWeight: "500",
  },
  gradientBackground: {
    flex: 1,
    paddingHorizontal: 24,
    backgroundColor: "#ffffff",
  },
  header: {
    justifyContent: "flex-end",
    paddingTop: 16,
    paddingBottom: 8,
  },
  skipButton: {
    alignItems: "center",
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  skipText: {
    color: "rgba(0, 0, 0, 0.7)",
    fontWeight: "600",
    fontSize: 14,
  },
  contentContainer: {
    flex: 1,
    justifyContent: "center",
    maxWidth: 400,
    alignSelf: "center",
    width: "100%",
  },
  logoContainer: {
    alignItems: "center",
    marginBottom: 40,
  },
  logoPlaceholder: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: "rgba(220, 53, 69, 0.1)",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 16,
  },
  appTitle: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#DC3545",
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: "rgba(0, 0, 0, 0.7)",
    textAlign: "center",
  },
  formContainer: {
    gap: 16,
  },
  inputWrapper: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(250, 250, 250, 0.05)",
    borderRadius: 16,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.2)",
    paddingHorizontal: 16,
    minHeight: 56,
  },
  inputIcon: {
    marginRight: 12,
    color: "rgba(0, 0, 0, 0.2)",
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: "rgba(0, 0, 0, 0.7)",
    paddingVertical: 0,
  },
  eyeIcon: {
    padding: 4,
    marginLeft: 8,
    color: "rgba(0, 0, 0, 0.2)",
  },
  errorContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255, 59, 48, 0.1)",
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: "rgba(255, 59, 48, 0.2)",
  },
  errorText: {
    color: "#FF3B30",
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
  },
  optionsRow: {
    justifyContent: "space-between",
    alignItems: "center",
  },
  checkboxContainer: {
    alignItems: "center",
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    color: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "transparent",
  },
  checkboxChecked: {
    borderColor: "#DC3545",
  },
  checkboxText: {
    color: "rgba(0, 0, 0, 0.7)",
    fontSize: 14,
  },
  forgotPassword: {
    color: "rgba(0, 0, 0, 0.7)",
    fontSize: 14,
    fontWeight: "500",
  },
  loginButton: {
    backgroundColor: "#DC3545",
    borderRadius: 16,
    minHeight: 56,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 8,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  loginButtonText: {
    color: "#ffffff",
    fontSize: 16,
    fontWeight: "600",
  },
  dividerContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: 8,
  },
  divider: {
    flex: 1,
    height: 1,
    backgroundColor: "rgba(220, 53, 69, 0.3)",
  },
  dividerText: {
    color: "rgba(220, 53, 69, 0.6)",
    paddingHorizontal: 16,
    fontSize: 14,
  },
  googleButton: {
    backgroundColor: "#DC3545",
    borderRadius: 16,
    minHeight: 56,
    justifyContent: "center",
    alignItems: "center",
  },
  googleIcon: {},
  googleButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "500",
  },
});
