{"or": "or", "signInWithGoogle": "Sign in with Google", "subscriptionInfo": "Subscription Information", "memberInformation": "Member Information", "financialOverview": "Financial Overview", "totalIncome": "Total Income", "totalExpenses": "Total Expenses", "profitLoss": "Profit/Loss", "subscriptionStats": "Subscription Statistics", "groupsOverview": "Groups Overview", "coachesOverview": "Coaches Overview", "myClub": "My Club", "members": "Members", "searchMembers": "Search members...", "subscriptionStart": "Subscription start", "subscriptionEnd": "Subscription end", "noMoreMembers": "No more members to load", "statusBadge": {"new": "New", "active": "Active", "trial": "Trial", "suspended": "Suspended", "archived": "Archived", "pending": "Pending"}, "clubSettings": "Club Settings", "manageClubPreferences": "Manage your club's preferences and notifications", "trialMemberships": "Trial Memberships", "trialMembershipsDescription": "Allow new members to try your services with a trial period", "memberNotifications": "Member Notifications", "memberNotificationsDescription": "Send automatic emails about subscription status to members", "newSubscriptionAlerts": "New Subscription Alerts", "newSubscriptionAlertsDescription": "Get notified when someone subscribes to your club", "expiredSubscriptionAlerts": "Expired Subscription Alerts", "expiredSubscriptionAlertsDescription": "Get notified when a subscription expires", "showClubToPublic": "Show Club to Public", "showClubToPublicDescription": "Make your club visible in public listings", "saveChanges": "Save Changes", "loadingSettings": "Loading settings...", "settingsSavedSuccessfully": "Setting<PERSON> saved successfully", "error": "Error", "failedToLoadSettings": "Failed to load settings. Please try again.", "failedToSaveSettings": "Failed to save settings. Please try again.", "scanMemberQrCode": "Scan Member QR Code", "positionQrCodeInFrame": "Position the QR code within the frame", "requestingCameraPermission": "Requesting camera permission...", "qrCode": "QR Code", "accountStatus": "Account Status", "hasAccount": "Has Account", "noAccount": "No Account", "clubStatus": "Club Status", "clubMember": "Club Member", "notClubMember": "Not Club Member", "scanAgain": "<PERSON><PERSON>", "tryAgain": "Try Again", "invalidQrCode": "Invalid QR code format", "scanFailed": "Failed to scan QR code", "qrcodeScanner": "Code Scanner", "qrcodeScannerHeader": "Scan QR Code", "camera": "Camera", "clubCamera": "Use club camera", "language": "English", "languageCode": "en", "profile": "Profile", "settings": "Settings", "logout": "Logout", "selectLanguage": "Select Language", "done": "Done", "clubs": "Clubs", "events": "Events", "news": "News", "subscription": "Subscription", "subscriptions": "Subscriptions", "payment": "Payment", "paymentMethod": "Payment Method", "paymentHistory": "Payment History", "paymentStatus": "Payment Status", "paymentDate": "Payment Date", "paymentAmount": "Payment Amount", "workout": "Workout", "workoutHistory": "Workout History", "workoutDate": "Workout Date", "workoutDuration": "Workout Duration", "workoutType": "Workout Type", "workoutIntensity": "Workout Intensity", "workoutLocation": "Workout Location", "name": "Name", "fullName": "Full Name", "email": "Email", "phone": "Phone Number", "editProfile": "Edit Profile", "membershipDetails": "Membership Details", "memberId": "Member ID", "membershipType": "Membership Type", "joinDate": "Join Date", "status": "Status", "active": "Active", "tapToSeeQrCode": "Tap to see QR code", "tapToSeeProfile": "Tap to see profile", "howToUseQrCode": "How to use QR Code", "qrInstruction1": "Save this QR code", "qrInstruction2": "Don't share it", "qrInstruction3": "Keep it safe", "updateName": "Update Name", "firstName": "First Name", "lastName": "Last Name", "gender": "Gender", "update": "Update", "cancel": "Cancel", "version": "Version", "helpAndFaq": "Help & FAQ", "contactSupport": "Contact Support", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "joinUs": "Join GoGymium today", "about": "About GoGymium", "allRightsReserved": "All Rights Reserved", "madeWithLoveBy": "Made with love by GoGymium team", "noActiveSubscriptions": "No Active Subscriptions", "noSubscriptionsDescription": "You haven't subscribed to any gym or fitness program yet.", "exploreClubs": "Explore Clubs", "pleaseLoginSubscriptions": "Please Login to view your subscriptions", "goToLogin": "Go to login", "startDate": "Start Date", "endDate": "End Date", "price": "Price", "discount": "Discount", "suspended": "Suspended", "login": "<PERSON><PERSON>", "newSubscription": "New Subscription", "cancelSubscription": "Cancel Subscription", "addEditSubscription": "Add/Edit Subscription", "subscriptionGroup": "Subscription Group", "selectGroup": "Select group", "durationMonths": "Duration (Months)", "enterDuration": "Enter duration", "customPrice": "Custom Price", "enterPrice": "Enter price", "discountPercentage": "Discount (%)", "enterDiscount": "Enter discount", "saveSubscription": "Save Subscription", "subscriptionHistory": "Subscription History", "noSubscriptionHistory": "No subscription history", "signIn": "Sign in", "signInToContinue": "Sign in to continue", "password": "Password", "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "skipLogin": "<PERSON><PERSON>", "dontHaveAccount": "Don't have an account?", "signUp": "Sign up", "pleaseEnterBothEmailAndPassword": "Please enter both email and password", "invalidCredentials": "Invalid credentials", "invalidCredentialsPleaseTryAgain": "Invalid credentials. Please try again.", "errorCheckingSession": "Error checking session. Please try again.", "searchTrainingPlaceholder": "Find Your Gym", "searchLocationPlaceholder": "Nearby (your location)", "openUntil": "Open until {{time}}", "statusUnknown": "Status unknown", "loadMore": "Load more", "website": "Website", "address": "Address", "clubDetails": {"days": {"Monday": "Monday", "Tuesday": "Tuesday", "Wednesday": "Wednesday", "Thursday": "Thursday", "Friday": "Friday", "Saturday": "Saturday", "Sunday": "Sunday"}, "subscribe": "Subscribe", "alreadySubscribed": "Already Subscribed", "subscriptionConfirmation": "Subscription Confirmation", "confirmSubscribe": "Confirm Subscribe", "subscriptionSuccess": "Subscription request sent successfully!", "subscriptionError": "You are already subscribed to this club.", "loginRequired": "Please login to subscribe", "amenities": "Amenities", "workingHours": "Working Hours", "contactInfo": "Contact Information", "location": "Location", "back": "Back", "openUntil": "Open until {{time}} today", "closed": "Closed", "featuredAmenities": "FEATURED AMENITIES", "meetTrainers": "MEET OUR TRAINERS", "gymHours": "GYM HOURS", "ourGroups": "OUR GROUPS", "actionRequired": "Action Required", "cancel": "Cancel", "confirm": "Confirm", "tryClub": "Try this club", "joinNow": "Join now", "alreadySubscribedMessage": "You are already subscribed to this club", "subscriptionInstructions": "After joining the club, you must validate your subscription. Please make the payment at the club reception, and the manager will confirm your subscription.\n\nMake sure all your information is correct to facilitate the subscription process. You can update your information from your profile, but after validation, only the club manager can make changes.\n\nOnce validated, your subscription details will be locked."}, "amenities": {"advancedAirFiltration": {"name": "Advanced Air Filtration", "description": "Advanced air filtration system for a clean and healthy environment."}, "babysitting": {"name": "Babysitting", "description": "Our babysitting service offers a safe and fun environment for your children. Our trained staff engage kids with activities, allowing you to enjoy some free time. Available for both short and extended periods."}, "basketballCourt": {"name": "Basketball Court", "description": "Basketball court for full or half-court games."}, "cardioMachines": {"name": "Cardio Machines", "description": "State-of-the-art cardio machines including treadmills, ellipticals, and stationary bikes."}, "childcare": {"name": "Childcare", "description": "Childcare services"}, "climbingWall": {"name": "Climbing Wall", "description": "Climbing wall for indoor rock climbing activities."}, "fitnessCenter": {"name": "Fitness Center", "description": "Stay fit with our state-of-the-art fitness center. Equipped with the latest machines and free weights, it caters to all your workout needs. Whether you're a fitness enthusiast or maintaining your routine, we've got you covered."}, "freeWeights": {"name": "Free Weights", "description": "Free weights area with dumbbells, barbells, and kettlebells for strength training."}, "functionalTrainingArea": {"name": "Functional Training Area", "description": "Functional training area equipped with TRX, battle ropes, and other versatile equipment."}, "groupFitnessStudios": {"name": "Group Fitness Studios", "description": "Group fitness studios offering classes like yoga, spinning, and aerobics."}, "hydrotherapyPool": {"name": "Hydrotherapy Pool", "description": "Hydrotherapy pool for therapeutic water exercises."}, "indoorTrack": {"name": "Indoor Track", "description": "Indoor track for running or walking."}, "juiceBarCaf": {"name": "Juice Bar/Café", "description": "Juice bar or café offering healthy snacks and beverages."}, "laundry": {"name": "<PERSON><PERSON><PERSON>", "description": "Our convenient laundry service takes care of all your washing needs. Drop off your clothes, and our professional team will wash, dry, and fold them, returning them fresh and clean. Save time and enjoy your stay."}, "lockerRooms": {"name": "Locker Rooms", "description": "Locker rooms with secure storage and changing areas for members."}, "meditationRelaxationArea": {"name": "Meditation/Relaxation Area", "description": "Meditation and relaxation area for quiet and peaceful activities."}, "outdoorTrainingArea": {"name": "Outdoor Training Area", "description": "Outdoor training area for various outdoor fitness activities."}, "parking": {"name": "Parking", "description": "Parking facilities for convenient member access."}, "personalTrainingServices": {"name": "Personal Training Services", "description": "Personal training services with certified professionals for customized fitness plans."}, "restaurant": {"name": "Restaurant", "description": "On-site restaurant"}, "retailShop": {"name": "Retail Shop", "description": "Retail shop selling workout gear and supplements."}, "saunaSteamRoom": {"name": "Sauna/Steam Room", "description": "Sauna and steam rooms for relaxation and detoxification."}, "showers": {"name": "Showers", "description": "Shower facilities for members to freshen up after workouts."}, "spaWellnessCenter": {"name": "Spa/Wellness Center", "description": "Access to spa or wellness center"}, "squashRacquetballCourts": {"name": "Squash/Racquetball Courts", "description": "Squash or racquetball courts for racquet sports enthusiasts."}, "strengthTrainingMachines": {"name": "Strength Training Machines", "description": "Strength training machines targeting various muscle groups for comprehensive workouts."}, "swimmingPool": {"name": "Swimming Pool", "description": "Enjoy our pristine swimming pool, perfect for swimming laps, cooling off, or lounging by the water. With comfortable seating and a serene atmosphere, it's an ideal spot to relax and unwind."}, "tennisCourt": {"name": "Tennis Court", "description": "Access to tennis court"}, "towelService": {"name": "Towel Service", "description": "Towel service providing clean towels for members."}, "wifi": {"name": "Wi-Fi", "description": "Free Wi-Fi available"}}, "noSubscription": "No Subscription", "notSubscribedToGroups": "Not subscribed to any group", "groups": "Groups", "group": "Group", "groupsScreen": {"availableGroups": "Available Groups", "noGroups": "No groups available at the moment", "withCoach": "with {{name}}", "members": "{{count}} Members", "ageRange": "{{min}} - {{max}} years", "schedule": "Schedule", "closed": "Closed", "pricePerMonth": "Price per month", "tryGroup": "Try Group", "subscribe": "Subscribe", "alreadySubscribed": "You are already subscribed to this club", "actionRequired": "Action Required", "cancel": "Cancel", "confirm": "Confirm", "days": {"MON": "MON", "TUE": "TUE", "WED": "WED", "THU": "THU", "FRI": "FRI", "SAT": "SAT", "SUN": "SUN"}}, "submit": "Submit", "addUserPhotoAndName": "Add user photo and name", "tapToSelectImage": "Tap to select an image", "takePhoto": "Take Photo", "chooseFromLibrary": "Choose from Library", "selectImage": "Select Image", "chooseOption": "Choose an option", "permissionError": "Permission Error", "cameraPermissionDenied": "Camera permission not granted. Please enable camera permissions in your device settings.", "photoLibraryPermissionDenied": "Photo library permission not granted. Please enable photo library permissions in your device settings.", "errorMessages": {"cameraUnavailable": "Camera not available on this device", "selectImageRequired": "Please select an image", "nameRequired": "Please enter your name", "cameraError": "Failed to open camera", "galleryError": "Failed to open photo gallery", "savingError": "Failed to save profile"}, "success": "Success", "profileUpdated": "Profile updated successfully", "selectStatus": "Select Status", "reset": "Reset", "allStatuses": "All Statuses", "selectedStatuses": "{{count}} statuses selected", "filterByStatus": "Filter by Status", "resetFilters": "Reset Filters", "notifications": "Notifications", "recentNotifications": "Your recent notifications", "noNotifications": "You have no notifications", "loadingNotifications": "Loading notifications...", "markAllAsRead": "<PERSON> as <PERSON>", "close": "Close", "failedToLoadNotifications": "Failed to load notifications. Please try again.", "failedToMarkAsRead": "Failed to mark notification as read.", "failedToMarkAllAsRead": "Failed to mark all notifications as read.", "failedToDeleteNotification": "Failed to delete notification.", "grantPermission": "Please grant permission to access the photo library to upload a photo.", "changePassword": "Change Password"}