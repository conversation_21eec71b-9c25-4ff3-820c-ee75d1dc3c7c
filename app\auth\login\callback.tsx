import { useEffect, useState } from "react";
import { View, Text, ActivityIndicator, StyleSheet, Platform } from "react-native";
import { useRouter, useLocalSearchParams } from "expo-router";
import AuthService from "@/services/auth.service";

export default function GoogleCallback() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    console.log("GoogleCallback mounted");
    const handleCallback = async () => {
      try {
        // Extract all OAuth parameters
        const { code, state, scope, authuser, prompt, error: oauthError, error_description } = params;

        // Check for OAuth errors
        if (oauthError) {
          throw new Error(`OAuth error: ${oauthError} - ${error_description}`);
        }

        if (!code) {
          throw new Error("No authorization code received");
        }

        // Call the backend to authenticate with all OAuth parameters
        const response = await AuthService.authenticateWithGoogle({
          code: code as string,
          state: state as string,
          scope: scope as string,
          authuser: authuser as string,
          prompt: prompt as string,
        });

        if (response?.accessToken) {
          // Navigate to home on successful authentication
          router.replace("/(tabs)/user/clubs");
        } else {
          throw new Error("No access token received from server");
        }
      } catch (error) {
        console.error("Google callback error:", error);
        setError(error instanceof Error ? error.message : "An error occurred during authentication");
      } finally {
        setIsLoading(false);
      }
    };

    handleCallback();
  }, [params]);

  return (
    <View style={styles.container}>
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#DC3545" />
          <Text style={styles.loadingText}>Authenticating with Google...</Text>
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <Text style={styles.retryText} onPress={() => router.replace("/auth/login")}>
            Return to Login
          </Text>
        </View>
      ) : null}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#ffffff",
    padding: 20,
  },
  loadingContainer: {
    alignItems: "center",
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: "#333",
  },
  errorContainer: {
    alignItems: "center",
  },
  errorText: {
    color: "#DC3545",
    fontSize: 16,
    textAlign: "center",
    marginBottom: 16,
  },
  retryText: {
    color: "#007AFF",
    fontSize: 16,
    textDecorationLine: "underline",
  },
});
