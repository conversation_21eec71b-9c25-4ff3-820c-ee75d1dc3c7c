import React, { useState, useEffect, useRef } from "react";
import { View, Text, TouchableOpacity, StyleSheet, Switch, Modal, ScrollView, Image, Alert, I18nManager, TextInput, Animated } from "react-native";
import { useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "../../../../context/ThemeContext";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useTranslation } from "react-i18next";
import AuthService from "@/services/auth.service";
import { REACT_APP_UPLOAD_URL } from "@/constants/server";
import * as Updates from "expo-updates";
import { IMAGES_URL } from "@/config/api.config";

type Subscription = {
  type: string;
  status: "Active" | "Suspended";
  startDate: string;
  endDate: string;
  price: number;
};

export default function ProfileScreen() {
  const router = useRouter();
  const theme = {
    background: "#fff",
    text: "#000",
    secondaryText: "#666",
    border: "#eee",
    card: "#fff",
    sectionHeader: "#000",
    searchBackground: "#f5f5f5",
    categoryTag: "#007AFF",
  };
  const [showLanguagePicker, setShowLanguagePicker] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState({
    name: "Français",
    code: "fr",
  });
  const { i18n, t } = useTranslation();
  const languages = [
    { name: "English", code: "en" },
    { name: "Français", code: "fr" },
    { name: "العربية", code: "ar" },
  ];

  const isRTL = i18n.language === "ar";

  // Set initial language based on stored preference
  useEffect(() => {
    const loadLanguagePreference = async () => {
      try {
        const storedLanguage = await AsyncStorage.getItem("user-language");
        if (storedLanguage) {
          const langObj = languages.find((lang) => lang.code === storedLanguage);
          if (langObj) {
            setSelectedLanguage(langObj);
            i18n.changeLanguage(storedLanguage);
          }
        }
      } catch (error) {
        console.error("Error loading language preference:", error);
      }
    };

    loadLanguagePreference();
  }, []);

  const [user, setUser] = useState<any | null>(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [showNameUpdateModal, setShowNameUpdateModal] = useState(false);
  const [newFirstName, setNewFirstName] = useState("");
  const [newLastName, setNewLastName] = useState("");

  // Add flip animation states
  const [isFlippedProfile, setIsFlippedProfile] = useState(false);
  const [isFlippedQR, setIsFlippedQR] = useState(false);
  const flipAnimProfile = useRef(new Animated.Value(0)).current;
  const flipAnimQR = useRef(new Animated.Value(0)).current;

  // Create interpolations for the flip animations
  const flipInterpolateProfile = flipAnimProfile.interpolate({
    inputRange: [0, 180],
    outputRange: ["0deg", "180deg"],
  });

  const flipInterpolateQR = flipAnimQR.interpolate({
    inputRange: [0, 180],
    outputRange: ["0deg", "180deg"],
  });

  const backInterpolateProfile = flipAnimProfile.interpolate({
    inputRange: [0, 180],
    outputRange: ["180deg", "360deg"],
  });

  const backInterpolateQR = flipAnimQR.interpolate({
    inputRange: [0, 180],
    outputRange: ["180deg", "360deg"],
  });

  // Flip animation functions
  const flipProfileCard = () => {
    if (isFlippedProfile) {
      Animated.timing(flipAnimProfile, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(flipAnimProfile, {
        toValue: 180,
        duration: 500,
        useNativeDriver: true,
      }).start();
    }
    setIsFlippedProfile(!isFlippedProfile);
  };

  const flipQRCard = () => {
    if (isFlippedQR) {
      Animated.timing(flipAnimQR, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(flipAnimQR, {
        toValue: 180,
        duration: 500,
        useNativeDriver: true,
      }).start();
    }
    setIsFlippedQR(!isFlippedQR);
  };

  useEffect(() => {
    const checkUserSession = async () => {
      try {
        const userSession = await AsyncStorage.getItem("userSession");
        setIsLoggedIn(!!userSession);

        if (userSession) {
          setUser(JSON.parse(userSession));
        }
      } catch (error) {
        console.error("Error checking user session:", error);
      }
    };

    checkUserSession();
  }, []);

  const handleLogout = async () => {
    try {
      // Clear all stored data
      await AsyncStorage.multiRemove(["userSession", "rememberedUser"]);
      // Navigate to login screen
      router.replace("/auth/login");
    } catch (error) {
      console.error("Error during logout:", error);
      Alert.alert("Error", "An error occurred during logout. Please try again.");
    }
  };

  const handleUpdateName = async () => {
    try {
      await AuthService.updateProfile({
        firstName: newFirstName,
        lastName: newLastName,
      });
      setShowNameUpdateModal(false);
      // Refresh user data or update local state
    } catch (error) {
      console.error("Error updating name:", error);
    }
  };

  return (
    <ScrollView
      showsVerticalScrollIndicator={false}
      contentInsetAdjustmentBehavior="automatic"
      style={[styles.container, { backgroundColor: theme.background }]}
    >
      {isLoggedIn && (
        <>
          <View style={styles.section}>
            <TouchableOpacity activeOpacity={0.9} style={styles.flipCardContainer}>
              <View
                style={[
                  styles.profileSection,
                  {
                    backgroundColor: "#FFFFFF",
                    borderColor: "rgba(0, 0, 0, 0.1)",
                    borderWidth: 1,
                  },
                ]}
              >
                <View style={styles.profileContainer}>
                  <Image
                    source={{
                      uri: `${IMAGES_URL}${user?.imageUrl}`,
                    }}
                    style={[
                      styles.profileImage,
                      {
                        borderColor: "#007AFF",
                      },
                    ]}
                  />

                  <Text
                    style={[
                      styles.fullName,
                      {
                        color: "#000000",
                        textAlign: isRTL ? "right" : "left",
                      },
                    ]}
                  >
                    {user?.fullname}
                  </Text>
                  <View
                    style={[
                      styles.contactItem,
                      {
                        flexDirection: isRTL ? "row-reverse" : "row",
                      },
                    ]}
                  >
                    <Ionicons
                      name="mail-outline"
                      size={14}
                      color={"rgba(0, 0, 0, 0.6)"}
                      style={[
                        styles.contactIcon,
                        {
                          marginRight: isRTL ? 0 : 8,
                          marginLeft: isRTL ? 8 : 0,
                        },
                      ]}
                    />
                    <Text
                      style={[
                        styles.contactInfo,
                        {
                          color: "rgba(0, 0, 0, 0.6)",
                          textAlign: isRTL ? "right" : "left",
                        },
                      ]}
                    >
                      {user?.email}
                    </Text>
                  </View>
                </View>
              </View>
            </TouchableOpacity>
          </View>
        </>
      )}

      <ScrollView style={styles.section}>
        {/* Account & Subscription */}
        <View style={[styles.sectionDivider, { backgroundColor: theme.card }]} />

        {isLoggedIn && (
          <>
            <TouchableOpacity
              style={[
                styles.settingItem,
                {
                  borderBottomColor: theme.card,
                  flexDirection: isRTL ? "row-reverse" : "row",
                  justifyContent: "space-between",
                },
              ]}
              onPress={() => setShowNameUpdateModal(true)}
            >
              <View style={[styles.settingLeft, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
                <Ionicons name="person-outline" size={24} color={theme.text} />
                <Text
                  style={[
                    styles.settingText,
                    {
                      color: theme.text,
                      marginLeft: isRTL ? 0 : 16,
                      marginRight: isRTL ? 16 : 0,
                    },
                  ]}
                >
                  {t("editProfile")}
                </Text>
              </View>
              <Ionicons name={isRTL ? "chevron-back" : "chevron-forward"} size={20} color={theme.secondaryText} />
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.settingItem,
                {
                  borderBottomColor: theme.card,
                  flexDirection: isRTL ? "row-reverse" : "row",
                  justifyContent: "space-between",
                },
              ]}
              onPress={() => router.push("https://www.gogymium.com/Account/forgot-password")}
            >
              <View style={[styles.settingLeft, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
                <Ionicons name="lock-closed-outline" size={24} color={theme.text} />
                <Text
                  style={[
                    styles.settingText,
                    {
                      color: theme.text,
                      marginLeft: isRTL ? 0 : 16,
                      marginRight: isRTL ? 16 : 0,
                    },
                  ]}
                >
                  {t("changePassword")}
                </Text>
              </View>
              <Ionicons name={isRTL ? "chevron-back" : "chevron-forward"} size={20} color={theme.secondaryText} />
            </TouchableOpacity>
          </>
        )}

        {!isLoggedIn && (
          <TouchableOpacity
            style={[
              styles.settingItem,
              {
                borderBottomColor: theme.card,
                flexDirection: isRTL ? "row-reverse" : "row",
              },
            ]}
            onPress={() => router.push("/user/clubs")}
          >
            <View style={[styles.settingLeft, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
              <Ionicons name="log-in-outline" size={24} color={theme.text} />
              <Text
                style={[
                  styles.settingText,
                  {
                    color: theme.text,
                    marginLeft: isRTL ? 0 : 16,
                    marginRight: isRTL ? 16 : 0,
                  },
                ]}
              >
                {t("login")}
              </Text>
            </View>
          </TouchableOpacity>
        )}
        <TouchableOpacity
          style={[
            styles.settingItem,
            {
              borderBottomColor: theme.card,
              flexDirection: isRTL ? "row-reverse" : "row",
            },
          ]}
          onPress={() => setShowLanguagePicker(true)}
        >
          <View style={[styles.settingLeft, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
            <Ionicons name="language" size={24} color={theme.text} />
            <Text
              style={[
                styles.settingText,
                {
                  color: theme.text,
                  marginLeft: isRTL ? 0 : 16,
                  marginRight: isRTL ? 16 : 0,
                },
              ]}
            >
              {t("language")}
            </Text>
          </View>
          <View style={[styles.settingRight, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
            <Text
              style={[
                styles.settingValue,
                {
                  color: theme.secondaryText,
                  marginRight: isRTL ? 0 : 8,
                  marginLeft: isRTL ? 8 : 0,
                },
              ]}
            >
              {selectedLanguage.name}
            </Text>
            <Ionicons name={isRTL ? "chevron-back" : "chevron-forward"} size={20} color={theme.secondaryText} />
          </View>
        </TouchableOpacity>

        {/* Help & Support */}
        <View style={[styles.sectionDivider, { backgroundColor: theme.card }]} />

        <TouchableOpacity
          style={[
            styles.settingItem,
            {
              borderBottomColor: theme.card,
              flexDirection: isRTL ? "row-reverse" : "row",
            },
          ]}
          onPress={() => router.push("https://www.gogymium.com/contact")}
        >
          <View style={[styles.settingLeft, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
            <Ionicons name="mail-outline" size={24} color={theme.text} />
            <Text
              style={[
                styles.settingText,
                {
                  color: theme.text,
                  marginLeft: isRTL ? 0 : 16,
                  marginRight: isRTL ? 16 : 0,
                },
              ]}
            >
              {t("contactSupport")}
            </Text>
          </View>
          <Ionicons name={isRTL ? "chevron-back" : "chevron-forward"} size={20} color={theme.secondaryText} />
        </TouchableOpacity>
        {!isLoggedIn && (
          <TouchableOpacity
            style={[
              styles.settingItem,
              {
                borderBottomColor: theme.card,
                flexDirection: isRTL ? "row-reverse" : "row",
              },
            ]}
            onPress={() => router.push("https://www.gogymium.com/")}
          >
            <View style={[styles.settingLeft, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
              <Ionicons name="person-add-outline" size={24} color={theme.text} />
              <Text
                style={[
                  styles.settingText,
                  {
                    color: theme.text,
                    marginLeft: isRTL ? 0 : 16,
                    marginRight: isRTL ? 16 : 0,
                  },
                ]}
              >
                {t("joinUs")}
              </Text>
            </View>
            <Ionicons name={isRTL ? "chevron-back" : "chevron-forward"} size={20} color={theme.secondaryText} />
          </TouchableOpacity>
        )}

        <View
          style={[
            styles.settingItem,
            {
              borderBottomColor: theme.card,
              flexDirection: isRTL ? "row-reverse" : "row",
            },
          ]}
        >
          <View style={[styles.settingLeft, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
            <Ionicons name="information-circle-outline" size={24} color={theme.text} />
            <Text
              style={[
                styles.settingText,
                {
                  color: theme.text,
                  marginLeft: isRTL ? 0 : 16,
                  marginRight: isRTL ? 16 : 0,
                },
              ]}
            >
              {t("version")}
            </Text>
          </View>
          <Text style={[styles.settingValue, { color: theme.secondaryText }]}>1.0.0</Text>
        </View>

        {isLoggedIn && (
          <TouchableOpacity
            style={[
              styles.settingItem,
              {
                borderBottomColor: theme.card,
                flexDirection: isRTL ? "row-reverse" : "row",
              },
            ]}
            onPress={handleLogout}
          >
            <View style={[styles.settingLeft, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
              <Ionicons name="log-out-outline" size={24} color="#FF3B30" />
              <Text
                style={[
                  styles.settingText,
                  styles.logoutText,
                  {
                    marginLeft: isRTL ? 0 : 16,
                    marginRight: isRTL ? 16 : 0,
                  },
                ]}
              >
                {t("logout")}
              </Text>
            </View>
          </TouchableOpacity>
        )}

        <View style={styles.creditContainer}>
          <Text
            style={[
              styles.creditText,
              {
                color: theme.secondaryText,
                textAlign: "center",
              },
            ]}
          >
            2024 GoGymium. {t("allRightsReserved")}.
          </Text>
        </View>
      </ScrollView>

      {/* Language Picker Modal */}
      <Modal visible={showLanguagePicker} transparent animationType="fade" onRequestClose={() => setShowLanguagePicker(false)}>
        <TouchableOpacity style={styles.modalOverlay} activeOpacity={0} onPress={() => setShowLanguagePicker(false)}>
          <View style={[styles.modalContent, { backgroundColor: theme.background }]}>
            <View
              style={[
                styles.modalHeader,
                {
                  borderBottomColor: theme.card,
                  flexDirection: isRTL ? "row-reverse" : "row",
                },
              ]}
            >
              <Text
                style={[
                  styles.modalTitle,
                  {
                    color: theme.text,
                    textAlign: isRTL ? "right" : "left",
                  },
                ]}
              >
                {t("selectLanguage")}
              </Text>
              <TouchableOpacity onPress={() => setShowLanguagePicker(false)}>
                <Text style={{ color: theme.text }}>{t("done")}</Text>
              </TouchableOpacity>
            </View>
            <ScrollView>
              {languages.map((language) => (
                <TouchableOpacity
                  key={language.code}
                  style={[
                    styles.languageItem,
                    {
                      borderBottomColor: theme.card,
                      flexDirection: isRTL ? "row-reverse" : "row",
                    },
                  ]}
                  onPress={async () => {
                    await AsyncStorage.setItem("user-language", language.code);
                    i18n.changeLanguage(language.code);
                    I18nManager.forceRTL(language.code === "ar");
                    setSelectedLanguage(language);
                    setShowLanguagePicker(false);
                    Updates.reloadAsync();
                  }}
                >
                  <Text
                    style={[
                      styles.languageText,
                      {
                        color: theme.text,
                      },
                    ]}
                  >
                    {language.name}
                  </Text>
                  {selectedLanguage.code === language.code && <Ionicons name="checkmark" size={20} color={theme.text} />}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </TouchableOpacity>
      </Modal>

      <Modal visible={showNameUpdateModal} transparent animationType="fade" onRequestClose={() => setShowNameUpdateModal(false)}>
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: theme.background }]}>
            <View
              style={[
                styles.modalHeader,
                {
                  borderBottomColor: theme.border,
                  flexDirection: isRTL ? "row-reverse" : "row",
                },
              ]}
            >
              <Text
                style={[
                  styles.modalTitle,
                  {
                    color: theme.text,
                    textAlign: isRTL ? "right" : "left",
                  },
                ]}
              >
                {t("updateName")}
              </Text>
              <TouchableOpacity onPress={() => setShowNameUpdateModal(false)}>
                <Text style={{ color: theme.text }}>{t("cancel")}</Text>
              </TouchableOpacity>
            </View>
            <View style={styles.inputContainer}>
              <TextInput
                style={[
                  styles.modalInput,
                  {
                    borderColor: theme.border,
                    color: theme.text,
                    textAlign: isRTL ? "right" : "left",
                    writingDirection: isRTL ? "rtl" : "ltr",
                  },
                ]}
                placeholder={t("firstName")}
                placeholderTextColor={theme.secondaryText}
                value={newFirstName}
                onChangeText={setNewFirstName}
              />
              <TextInput
                style={[
                  styles.modalInput,
                  {
                    borderColor: theme.border,
                    color: theme.text,
                    textAlign: isRTL ? "right" : "left",
                    writingDirection: isRTL ? "rtl" : "ltr",
                  },
                ]}
                placeholder={t("lastName")}
                placeholderTextColor={theme.secondaryText}
                value={newLastName}
                onChangeText={setNewLastName}
              />
            </View>
            <TouchableOpacity style={styles.updateButton} onPress={handleUpdateName}>
              <Text style={styles.updateButtonText}>{t("update")}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    alignItems: "center",
    padding: 20,
    borderBottomWidth: 1,
  },
  profileSection: {
    marginTop: 32,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  profileContainer: {
    alignItems: "center",
    justifyContent: "center",
    flex: 1,
    width: "100%",
    height: 200,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: 12,
    borderWidth: 3,
    borderColor: "#007AFF",
  },
  fullName: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 8,
  },
  contactInfo: {
    fontSize: 14,
  },
  editProfileButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(0, 122, 255, 0.1)",
    padding: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
  },
  editButtonText: {
    marginLeft: 6,
    fontSize: 14,
    fontWeight: "600",
    color: "#007AFF",
  },
  qrContainer: {
    alignItems: "center",
    justifyContent: "center",
    flex: 1,
    width: "100%",
    height: 250,
  },
  qrCode: {
    backgroundColor: "#fff",
    width: 220,
    height: 220,
  },
  qrText: {
    fontSize: 14,
    fontWeight: "500",
  },
  avatarContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 16,
  },
  name: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 4,
  },
  email: {
    fontSize: 16,
  },
  section: {
    paddingHorizontal: 16,
    marginBottom: 64,
  },
  sectionDivider: {
    height: 3,
  },
  settingItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderBottomWidth: 1,
  },
  settingLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  settingRight: {
    flexDirection: "row",
    alignItems: "center",
  },
  settingText: {
    fontSize: 16,
    marginLeft: 16,
  },
  settingValue: {
    fontSize: 16,
    marginRight: 8,
  },
  logoutText: {
    color: "#FF3B30",
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
  },
  modalContent: {
    width: "100%",
    maxHeight: "80%",
    borderRadius: 20,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0, 0, 0, 0.1)",
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: "600",
  },
  modalInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    fontSize: 16,
  },
  updateButton: {
    backgroundColor: "#E13D3B",
    padding: 12,
    borderRadius: 8,
    alignItems: "center",
  },
  updateButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  inputContainer: {
    marginVertical: 16,
  },
  subscriptionList: {
    marginTop: 16,
  },
  subscriptionCard: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 2,
  },
  subscriptionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  subscriptionType: {
    fontSize: 18,
    fontWeight: "600",
  },
  subscriptionStatus: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    fontSize: 13,
    fontWeight: "500",
  },
  subscriptionDetail: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  subscriptionLabel: {
    fontSize: 14,
    opacity: 0.7,
  },
  subscriptionValue: {
    fontSize: 14,
    fontWeight: "500",
  },
  languageItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
  },
  languageText: {
    fontSize: 16,
  },
  creditContainer: {
    alignItems: "center",
    marginTop: 24,
    paddingVertical: 16,
  },
  creditText: {
    fontSize: 12,
    textAlign: "center",
    marginVertical: 2,
  },
  contactItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  contactIcon: {
    marginRight: 8,
  },
  flipCardContainer: {
    position: "relative",
    width: "100%",
    height: 220,
  },
  flipCard: {
    width: "100%",
    height: "100%",
    position: "absolute",
    backfaceVisibility: "hidden",
  },
  flipCardBack: {
    backfaceVisibility: "hidden",
  },
  backCardTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 20,
    color: "#000000",
  },
  membershipDetailItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
    paddingHorizontal: 20,
    marginBottom: 15,
  },
  membershipLabel: {
    fontSize: 16,
    color: "rgba(0, 0, 0, 0.6)",
  },
  membershipValue: {
    fontSize: 16,
    fontWeight: "500",
    color: "#000000",
  },
  qrInstructionsContainer: {
    alignItems: "center",
    justifyContent: "center",
    flex: 1,
    width: "100%",
    padding: 20,
  },
  instructionItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 15,
    width: "100%",
  },
  instructionNumber: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: "#007AFF",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  instructionNumberText: {
    color: "#FFFFFF",
    fontWeight: "bold",
    fontSize: 14,
  },
  instructionText: {
    fontSize: 16,
    color: "#000000",
    flex: 1,
  },
  flipHint: {
    fontSize: 12,
    color: "#007AFF",
    fontStyle: "italic",
  },
});
