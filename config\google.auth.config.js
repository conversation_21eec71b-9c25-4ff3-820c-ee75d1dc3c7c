// const GOOGLE_AUTH_CONFIG = {
//   CLIENT_ID: "595684359316-muh5ti47so3h87t9agb9nmfvoqpq4oj1.apps.googleusercontent.com",
//   CLIENT_SECRET: "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",
// };

// export default GOOGLE_AUTH_CONFIG;

/**
 * Google OAuth Configuration
 * Reads from google-auth.json and provides platform-specific client IDs
 */

import { Platform } from "react-native";
import googleAuthConfig from "./google-auth.json";

/**
 * Get the appropriate client ID based on the current platform
 * @returns {string} Google OAuth client ID
 */
export const getGoogleClientId = () => {
  switch (Platform.OS) {
    case "ios":
      return googleAuthConfig.ios.client_id;
    case "android":
      return googleAuthConfig.android.client_id;
    case "web":
    default:
      return googleAuthConfig.web.client_id;
  }
};

/**
 * Google OAuth scopes
 * Defines what user information we want to access
 */
export const GOOGLE_SCOPES = ["openid", "profile", "email"];

/**
 * OAuth configuration object
 */
export const GOOGLE_CONFIG = {
  clientId: getGoogleClientId(),
  scopes: GOOGLE_SCOPES,
  redirectUri:
    Platform.OS === "web" && typeof window !== "undefined"
      ? window.location.origin
      : "com.yourapp.redirect://oauth",
  responseType: "code",
  additionalParameters: {},
  customParameters: {
    prompt: "select_account",
  },
};
