{"or": "ou", "signInWithGoogle": "Se connecter avec Google", "subscriptionInfo": "Informations d'abonnement", "memberInformation": "Informations du Membre", "financialOverview": "A<PERSON>çu Financier", "totalIncome": "Revenu Total", "totalExpenses": "Dépenses Totales", "profitLoss": "Bénéfice/Perte", "subscriptionStats": "Statistiques des Abonnements", "groupsOverview": "Vue d'ensemble des Groupes", "coachesOverview": "Vue d'ensemble des Entraineurs", "myClub": "Mon Club", "members": "Me<PERSON><PERSON>", "searchMembers": "Chercher des membres...", "subscriptionStart": "Début de l'abonnement", "subscriptionEnd": "Fin de l'abonnement", "noMoreMembers": "Plus de membres à charger", "statusBadge": {"new": "Nouveau", "active": "Actif", "trial": "<PERSON><PERSON><PERSON>", "suspended": "Suspendu", "archived": "Archivé", "pending": "En attente"}, "clubSettings": "Paramètres du Club", "manageClubPreferences": "<PERSON><PERSON><PERSON> les préférences et notifications de votre club", "trialMemberships": "Adhésions d'Essai", "trialMembershipsDescription": "Autoriser les nouveaux membres à essayer vos services avec une période d'essai", "memberNotifications": "Notifications aux Membres", "memberNotificationsDescription": "Envoyer des e-mails automatiques sur le statut de l'abonnement aux membres", "newSubscriptionAlerts": "Alertes de Nouveaux Abonnements", "newSubscriptionAlertsDescription": "Être notifié lorsqu'une personne s'abonne à votre club", "expiredSubscriptionAlerts": "Alertes d'Abonnements Expirés", "expiredSubscriptionAlertsDescription": "Être notifié lorsqu'un abonnement expire", "showClubToPublic": "Afficher le Club au Public", "showClubToPublicDescription": "Rendre votre club visible dans les listes publiques", "saveChanges": "Enregistrer les Modifications", "loadingSettings": "Chargement des paramètres...", "settingsSavedSuccessfully": "Paramètres enregistrés avec succès", "error": "<PERSON><PERSON><PERSON>", "failedToLoadSettings": "Échec du chargement des paramètres. Veuillez réessayer.", "failedToSaveSettings": "Échec de l'enregistrement des paramètres. Veuillez réessayer.", "scanMemberQrCode": "Scanner le QR Code du membre", "positionQrCodeInFrame": "Placez le QR Code dans le cadre", "requestingCameraPermission": "Demande d'autorisation de la caméra...", "qrCode": "Code QR", "accountStatus": "Statut du compte", "hasAccount": "A un compte", "noAccount": "Pas de compte", "clubStatus": "Statut du club", "clubMember": "Membre du club", "notClubMember": "Non membre du club", "scanAgain": "Scanner à nouveau", "tryAgain": "<PERSON><PERSON><PERSON><PERSON>", "invalidQrCode": "Format de QR Code invalide", "scanFailed": "Échec de la lecture du QR Code", "qrcodeScanner": "Code Scanner", "qrcodeScannerHeader": "Scanner le code QR", "camera": "Caméra", "clubCamera": "Utilisez la caméra du club", "language": "Français", "languageCode": "fr", "profile": "Profil", "settings": "Paramètres", "logout": "Déconnexion", "selectLanguage": "Sélectionner la langue", "done": "<PERSON><PERSON><PERSON><PERSON>", "clubs": "Clubs", "events": "Événements", "news": "Actualités", "subscription": "Abonnement", "subscriptions": "Abonnements", "payment": "Paiement", "paymentMethod": "Méthode de paiement", "paymentHistory": "Historique des paiements", "paymentStatus": "Statut du paiement", "paymentDate": "Date de paiement", "paymentAmount": "Montant du paiement", "workout": "Entraînement", "workoutHistory": "Historique d'entraînement", "workoutDate": "Date d'entraînement", "workoutDuration": "Durée d'entraînement", "workoutType": "Type d'entraînement", "workoutIntensity": "Intensité d'entraînement", "workoutLocation": "Lieu d'entraînement", "name": "Nom", "fullName": "Nom complet", "email": "Email", "phone": "Numéro de téléphone", "editProfile": "Modifier le profil", "membershipDetails": "Détails de l'adhésion", "memberId": "ID de membre", "membershipType": "Type d'adhésion", "joinDate": "Date d'inscription", "status": "Statut", "active": "Actif", "tapToSeeQrCode": "Appuyez pour voir le code QR", "tapToSeeProfile": "Appuyez pour voir le profil", "howToUseQrCode": "Comment utiliser le code QR", "qrInstruction1": "Enregistrez ce code QR", "qrInstruction2": "Ne le partagez pas", "qrInstruction3": "Gardez-le en sécurité", "updateName": "Mettre à jour le nom", "firstName": "Prénom", "lastName": "Nom de famille", "gender": "Genre", "update": "Mettre à jour", "cancel": "Annuler", "version": "Version", "helpAndFaq": "Aide et FAQ", "contactSupport": "<PERSON>er le support", "privacyPolicy": "Politique de confidentialité", "termsOfService": "Conditions d'utilisation", "about": "À propos de GoGymium", "joinUs": "<PERSON><PERSON><PERSON><PERSON> GoGymium aujourd'hui", "allRightsReserved": "Tous droits réservés", "madeWithLoveBy": "Fait avec amour par l'équipe GoGymium", "noActiveSubscriptions": "Aucun abonnement actif", "noSubscriptionsDescription": "Vous n'êtes pas encore abonné à une salle de sport ou à un programme de fitness.", "exploreClubs": "Explorer les clubs", "pleaseLoginSubscriptions": "Veuillez vous connecter pour voir vos abonnements", "goToLogin": "Aller à la connexion", "startDate": "Date de début", "endDate": "Date de fin", "price": "Prix", "discount": "Réduction", "suspended": "Suspendu", "login": "Connexion", "newSubscription": "Nouvel abonnement", "cancelSubscription": "Annuler l'abonnement", "addEditSubscription": "Ajouter/Modifier l'abonnement", "subscriptionGroup": "Groupe d'abonnement", "selectGroup": "Sélectionner un groupe", "durationMonths": "Du<PERSON>e (Mois)", "enterDuration": "Entrer la durée", "customPrice": "Prix personnalisé", "enterPrice": "Entrer le prix", "discountPercentage": "Réduction (%)", "enterDiscount": "Entrer la réduction", "saveSubscription": "Enregistrer l'abonnement", "subscriptionHistory": "Historique des abonnements", "noSubscriptionHistory": "Aucun historique d'abonnement", "signIn": "Se connecter", "signInToContinue": "Connectez-vous pour continuer", "password": "Mot de passe", "rememberMe": "Se souvenir de moi", "forgotPassword": "Mot de passe oublié?", "skipLogin": "Ignorer la connexion", "dontHaveAccount": "Vous n'avez pas de compte?", "signUp": "S'inscrire", "pleaseEnterBothEmailAndPassword": "Veuillez entrer l'email et le mot de passe", "invalidCredentials": "Identifiants invalides", "invalidCredentialsPleaseTryAgain": "Identifiants invalides. Veuillez réessayer.", "errorCheckingSession": "Erreur lors de la vérification de la session. Veuillez réessayer.", "searchTrainingPlaceholder": "Trouver votre salle de sport", "searchLocationPlaceholder": "À proximité (votre emplacement)", "openUntil": "Ouvert jusqu'à {{time}}", "statusUnknown": "Statut inconnu", "loadMore": "Charger plus", "website": "Site web", "address": "<PERSON><PERSON><PERSON>", "clubDetails": {"days": {"Monday": "<PERSON><PERSON>", "Tuesday": "<PERSON><PERSON>", "Wednesday": "<PERSON><PERSON><PERSON><PERSON>", "Thursday": "<PERSON><PERSON>", "Friday": "<PERSON><PERSON><PERSON><PERSON>", "Saturday": "<PERSON><PERSON>", "Sunday": "<PERSON><PERSON><PERSON>"}, "subscribe": "<PERSON>'abonner", "alreadySubscribed": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "subscriptionConfirmation": "Confirmation d'abonnement", "confirmSubscribe": "Confirmer l'abonnement", "subscriptionSuccess": "De<PERSON>e d'abonnement envoyée avec succès !", "subscriptionError": "Vous êtes déjà abonné à ce club.", "loginRequired": "Veuillez vous connecter pour vous abonner", "amenities": "Équipements", "workingHours": "Heures d'ouverture", "contactInfo": "Informations de contact", "location": "Emplacement", "back": "Retour", "openUntil": "Ouvert jusqu'à {{time}} aujourd'hui", "closed": "<PERSON><PERSON><PERSON>", "featuredAmenities": "ÉQUIPEMENTS VEDETTES", "meetTrainers": "RENCONTREZ NOS ENTRAÎNEURS", "gymHours": "HEURES D'OUVERTURE", "ourGroups": "NOS GROUPES", "actionRequired": "Action requise", "cancel": "Annuler", "confirm": "Confirmer", "tryClub": "Essayer ce club", "joinNow": "Rejoindre maintenant", "alreadySubscribedMessage": "Vous êtes déjà abonné à ce club", "subscriptionInstructions": "Après avoir rejoint le club, vous devez valider votre abonnement. V<PERSON>illez effectuer le paiement à la réception du club, et le responsable confirmera votre abonnement.\n\nAssurez-vous que toutes vos informations sont correctes pour faciliter le processus d'abonnement. Vous pouvez mettre à jour vos informations depuis votre profil, mais après validation, seul le responsable du club peut apporter des modifications.\n\nUne fois validé, vos détails d'abonnement seront verrouillés."}, "amenities": {"advancedAirFiltration": {"name": "Filtration d'air avancée", "description": "Système de filtration d'air avancé pour un environnement propre et sain."}, "babysitting": {"name": "Garde d'enfants", "description": "Notre service de garde d'enfants offre un environnement sûr et amusant pour vos enfants. Notre personnel qualifié organise des activités pour divertir les enfants, vous permettant de profiter de votre temps libre. Disponible pour des périodes courtes ou prolongées."}, "basketballCourt": {"name": "Terrain de basket", "description": "Terrain de basket pour des matchs complets ou en demi-terrain."}, "cardioMachines": {"name": "Machines de cardio", "description": "Machines de cardio dernier cri, incluant tapis de course, elliptiques et vélos stationnaires."}, "childcare": {"name": "Garde d'enfants", "description": "Services de garde d'enfants."}, "climbingWall": {"name": "Mur d'escalade", "description": "Mur d'escalade pour des activités d'escalade en intérieur."}, "fitnessCenter": {"name": "Centre de fitness", "description": "Restez en forme grâce à notre centre de fitness ultra-moderne, équipé des dernières machines et poids libres. Que vous soyez passionné de sport ou que vous souhaitiez simplement entretenir votre routine, nous avons ce qu'il vous faut."}, "freeWeights": {"name": "Poids libres", "description": "Espace de poids libres avec haltères, barres et kettlebells pour l'entraînement de force."}, "functionalTrainingArea": {"name": "Zone d'entraînement fonctionnel", "description": "Espace d'entraînement fonctionnel équipé de TRX, cordes ondulatoires et autres équipements polyvalents."}, "groupFitnessStudios": {"name": "Studios de cours collectifs", "description": "Studios proposant des cours collectifs comme le yoga, le spinning et l’aérobic."}, "hydrotherapyPool": {"name": "Piscine d'hydrothérapie", "description": "Piscine d'hydrothérapie pour des exercices aquatiques thérapeutiques."}, "indoorTrack": {"name": "Piste intérieure", "description": "Piste intérieure pour courir ou marcher."}, "juiceBarCaf": {"name": "Bar à jus/Café", "description": "Bar à jus ou café proposant des encas et boissons saines."}, "laundry": {"name": "Blanchisserie", "description": "Notre service de blanchisserie s’occupe de tout votre linge. Déposez vos vêtements et notre équipe se charge de les laver, sécher et plier pour vous les rendre propres et frais. Gagnez du temps et profitez de votre séjour."}, "lockerRooms": {"name": "Vestiaires", "description": "Vestiaires avec espaces de rangement sécurisés et cabines de changement pour les membres."}, "meditationRelaxationArea": {"name": "Espace méditation/relaxation", "description": "Espace dédié à la méditation et à la détente."}, "outdoorTrainingArea": {"name": "Espace d'entraînement extérieur", "description": "Zone d'entraînement en plein air pour différentes activités sportives."}, "parking": {"name": "Parking", "description": "Parking à disposition pour faciliter l'accès des membres."}, "personalTrainingServices": {"name": "Coaching <PERSON><PERSON><PERSON><PERSON>", "description": "Services de coaching personnalisé avec des professionnels certifiés pour des programmes sur-mesure."}, "restaurant": {"name": "Restaurant", "description": "Restaurant sur place."}, "retailShop": {"name": "Boutique", "description": "Boutique vendant des vêtements de sport et compléments alimentaires."}, "saunaSteamRoom": {"name": "Sauna/Hammam", "description": "Sauna et hammam pour la détente et la détoxification."}, "showers": {"name": "Douches", "description": "Douches disponibles pour les membres après l'entraînement."}, "spaWellnessCenter": {"name": "Spa/Centre bien-être", "description": "Accès au spa ou centre de bien-être."}, "squashRacquetballCourts": {"name": "Terrains de squash/racquetball", "description": "Terrains de squash ou racquetball pour les amateurs de sports de raquette."}, "strengthTrainingMachines": {"name": "Machines de musculation", "description": "Machines de musculation ciblant différents groupes musculaires pour un entraînement complet."}, "swimmingPool": {"name": "Piscine", "description": "Profitez de notre piscine impeccable, idéale pour nager, se raf<PERSON><PERSON><PERSON>r ou se détendre au bord de l'eau. Avec des sièges confortables et une ambiance apaisante, c'est l'endroit parfait pour se relaxer."}, "tennisCourt": {"name": "Court de tennis", "description": "Accès au court de tennis."}, "towelService": {"name": "Service de serviettes", "description": "Service de serviettes propres à disposition des membres."}, "wifi": {"name": "Wi-Fi", "description": "Wi-Fi gratuit disponible."}}, "groups": "Groupes", "group": "Groupe", "noSubscription": "Pas d'abonnement", "notSubscribedToGroups": "Pas abonné à un groupe", "groupsScreen": {"availableGroups": "Groupes disponibles", "noGroups": "Aucun groupe disponible pour le moment", "withCoach": "avec {{name}}", "members": "{{count}} <PERSON><PERSON><PERSON>", "ageRange": "{{min}} - {{max}} ans", "schedule": "<PERSON><PERSON><PERSON>", "closed": "<PERSON><PERSON><PERSON>", "pricePerMonth": "Prix par mois", "tryGroup": "Essayer le groupe", "subscribe": "<PERSON>'abonner", "alreadySubscribed": "Vous êtes déjà abonné à ce club", "actionRequired": "Action requise", "cancel": "Annuler", "confirm": "Confirmer", "days": {"MON": "LUN", "TUE": "MAR", "WED": "MER", "THU": "JEU", "FRI": "VEN", "SAT": "SAM", "SUN": "DIM"}}, "submit": "So<PERSON><PERSON><PERSON>", "addUserPhotoAndName": "Ajouter une photo et un nom d'utilisateur", "tapToSelectImage": "Appuyez pour sélectionner une image", "takePhoto": "<PERSON><PERSON><PERSON> une photo", "chooseFromLibrary": "Choisir dans la galerie", "selectImage": "Sélectionner une image", "chooseOption": "Choisir une option", "permissionError": "<PERSON><PERSON><PERSON> de permission", "cameraPermissionDenied": "L'accès à la caméra n'est pas autorisé. Veuillez activer les autorisations de caméra dans les paramètres de votre appareil.", "photoLibraryPermissionDenied": "L'accès à la galerie n'est pas autorisé. Veuillez activer les autorisations de galerie dans les paramètres de votre appareil.", "errorMessages": {"cameraUnavailable": "Caméra non disponible sur cet appareil", "selectImageRequired": "Veuillez sélectionner une image", "nameRequired": "Veuillez entrer votre nom", "cameraError": "Impossible d'ouvrir la caméra", "galleryError": "Impossible d'ouvrir la galerie", "savingError": "Impossible d'enregistrer le profil"}, "success": "Su<PERSON>ès", "profileUpdated": "Profil mis à jour avec succès", "selectStatus": "Sélectionner le statut", "reset": "Réinitialiser", "allStatuses": "Tous les statuts", "selectedStatuses": "{{count}} statuts sélectionnés", "filterByStatus": "Filtrer par statut", "resetFilters": "Réinitialiser les filtres", "notifications": "Notifications", "recentNotifications": "Vos notifications récentes", "noNotifications": "Vous n'avez pas de notifications", "loadingNotifications": "Chargement des notifications...", "markAllAsRead": "Tout marquer comme lu", "close": "<PERSON><PERSON><PERSON>", "failedToLoadNotifications": "Échec du chargement des notifications. Veuillez réessayer.", "failedToMarkAsRead": "Échec du marquage de la notification comme lue.", "failedToMarkAllAsRead": "Échec du marquage de toutes les notifications comme lues.", "failedToDeleteNotification": "Échec de la suppression de la notification.", "grantPermission": "Veuillez accorder l'autorisation d'accès à la galerie pour télécharger une photo.", "changePassword": "Changer le mot de passe"}