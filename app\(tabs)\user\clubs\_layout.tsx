import { Stack } from "expo-router";
import { Colors } from "@/constants/Colors";
import { useTheme } from "@/context/ThemeContext";
import { StatusBar } from "expo-status-bar";
import { TouchableOpacity, View, Text, TextInput } from "react-native";
import { MapPin, Search } from "lucide-react-native";
import { useRouter } from "expo-router";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { useTranslation } from "react-i18next";

const Layout = () => {
  const { t } = useTranslation();

  const backgroundColor = Colors.light.background;
  console.log("User Clubs Layout");
  return (
    <Stack
      screenOptions={{
        headerShadowVisible: false,
        contentStyle: { backgroundColor: backgroundColor },
      }}
    >
      <Stack.Screen
        name="index"
        options={{
          title: t("clubs"),

          headerLargeTitle: true,
          headerTitle: t("clubs"),
          headerTitleAlign: "center",

          // headerRight: () => (
          //   <TouchableOpacity
          //     onPress={() => router.push("/map")}
          //     style={styles.headerRight}
          //   >
          //     <Text style={[
          //       styles.cityText,
          //       { color: Colors[isDark ? 'dark' : 'light'].text }
          //     ]}>
          //       Select city
          //     </Text>
          //     <MapPin
          //       size={24}
          //       color={Colors[isDark ? 'dark' : 'light'].text}
          //     />
          //   </TouchableOpacity>
          // ),
        }}
      />
      <StatusBar style="auto" />
    </Stack>
  );
};

const styles = {
  headerRight: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: 16,
    gap: 8,
  },
  cityText: {
    fontSize: 16,
    fontWeight: "500",
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: 16,
    marginBottom: 8,
    paddingHorizontal: 12,
    height: 44,
    borderRadius: 8,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    height: "100%",
  },
};

export default Layout;
