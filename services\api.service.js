import API_CONFIG from "../config/api.config";
import TokenService from "./token.service";
// Import dynamically to avoid circular dependency
let TokenValidatorService;
let AuthService;
import("./token-validator.service").then((module) => {
  TokenValidatorService = module.default;
});
import("./auth.service").then((module) => {
  AuthService = module.default;
});

class ApiService {
  static async request(endpoint, options = {}, retryCount = 0) {
    // Maximum number of retry attempts
    const MAX_RETRIES = 0; // No more retries since we're removing refresh token logic

    try {
      const token = await TokenService.getToken();
      const defaultOptions = {
        headers: {
          "Content-Type":
            options?.body !== undefined && options?.body instanceof FormData
              ? "multipart/form-data"
              : "application/json",
          ...(token ? { Authorization: `Bearer ${token}` } : {}),
        },
        timeout: API_CONFIG.TIMEOUT,
        ...options,
      };

      const response = await fetch(
        `${API_CONFIG.BASE_URL}${endpoint}`,
        defaultOptions
      );

      // Handle authentication errors (401)
      if (response.status === 401) {
        // Clear tokens and force logout
        await TokenService.clearTokens();
        // You can add navigation to login screen here if needed
        throw new Error("Authentication expired. Please log in again.");
      }

      // Handle other response statuses
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const error = new Error(
          errorData.message || `HTTP error! status: ${response.status}`
        );
        error.status = response.status;
        error.data = errorData;
        throw error;
      }

      // Parse and return the response
      try {
        const data = await response.json();
        return data;
      } catch (parseError) {
        // If response is empty or not JSON, return null
        if (response.status === 204) return null;
        throw new Error("Failed to parse response");
      }
    } catch (error) {
      console.error("API request failed:", error);
      throw error;
    }
  }

  static get(endpoint) {
    return this.request(endpoint, { method: "GET" });
  }

  static post(endpoint, data) {
    return this.request(endpoint, {
      method: "POST",
      body: data instanceof FormData ? data : JSON.stringify(data),
    });
  }

  static put(endpoint, data) {
    return this.request(endpoint, {
      method: "PUT",
      body: data instanceof FormData ? data : JSON.stringify(data),
    });
  }

  static delete(endpoint) {
    return this.request(endpoint, { method: "DELETE" });
  }
}

export default ApiService;
