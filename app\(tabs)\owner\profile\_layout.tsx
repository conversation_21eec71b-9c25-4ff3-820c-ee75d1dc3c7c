import { Stack } from "expo-router";
import { Colors } from "@/constants/Colors";

const Layout = () => {
  console.log("Owner Profile Layout");
  return (
    <Stack
      screenOptions={{
        headerShadowVisible: false,
        contentStyle: { backgroundColor: Colors.light.background },
      }}
    >
      <Stack.Screen
        name="index"
        options={{
          title: "Profile",
          headerLargeTitle: true,
          //   headerSearchBarOptions: {
          //     placeholder: 'Find your perfect gym',
          //     tintColor: Colors.light.text,
          //   },
        }}
      />
    </Stack>
  );
};
export default Layout;
