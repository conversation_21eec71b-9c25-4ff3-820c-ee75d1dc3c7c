{"expo": {"name": "GoGymium", "slug": "gogymium", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "gogymium", "userInterfaceStyle": "automatic", "newArchEnabled": false, "ios": {"bundleIdentifier": "com.murad.elm.gogymium", "supportsTablet": true, "infoPlist": {"NSCameraUsageDescription": "We need access to your camera to take a photo", "NSPhotoLibraryUsageDescription": "We need access to your photo library to select an image", "NSPhotoLibraryAddUsageDescription": "We need access to save images", "ITSAppUsesNonExemptEncryption": false}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/icon.png", "backgroundColor": "#ffffff"}, "permissions": ["android.permission.CAMERA", "android.permission.RECORD_AUDIO", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE"], "package": "com.murad.elm.gogymium", "intentFilters": [{"action": "VIEW", "data": [{"scheme": "gogymium", "host": "auth", "pathPrefix": "/login/callback"}], "category": ["BROWSABLE", "DEFAULT"]}]}, "web": {"bundler": "metro", "output": "single", "favicon": "./assets/images/favicon.png"}, "plugins": [["@react-native-google-signin/google-signin"], ["expo-router", {"origin": false, "asyncRoutes": false}], "expo-web-browser", "expo-localization", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-image-picker", ["expo-camera", {"cameraPermission": "Allow $(PRODUCT_NAME) to access your camera", "microphonePermission": "Allow $(PRODUCT_NAME) to access your microphone", "recordAudioAndroid": true}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "6d23f572-8933-4f96-8774-20cd3257b16a"}}, "owner": "murad.elm"}}